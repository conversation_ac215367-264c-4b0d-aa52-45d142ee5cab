<template>
  <view class="info-card">
    <view class="card-header">
      <view :class="['status-tag-detail', getRegistrationStatusClass(calculatedRegistrationStatus)]">
        <image class="status-bg-image" :src="detailBgUrl" mode="aspectFit"></image>
        <text class="status-text">{{ formatRegistrationStatus(calculatedRegistrationStatus) }}</text>
      </view>
      <view class="event-title-section">
        <text class="event-title">{{ localEvent.title || '' }}</text>
      </view>
    </view>

    <view class="info-row">
      <image class="info-icon" :src="detailTimeIconUrl" mode="aspectFit"></image>
      <text class="info-text">{{ formatEventTime }}</text>
    </view>
    <view class="info-row">
      <image class="info-icon" :src="detailLocationIconUrl" mode="aspectFit"></image>
      <text class="info-text">{{ formatEventLocation(localEvent) }}</text>
    </view>
    <view class="info-row">
      <image class="info-icon" :src="detailUserIconUrl" mode="aspectFit"></image>
      <rich-text :nodes="remainingSpotsNodes" class="info-text"></rich-text>
    </view>
  </view>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { formatEventStatus, getStatusClass } from '@/utils/tools.js'
import { formatDate } from '@/utils/date.js'
import { formatEventLocation } from '@/utils/location.js'

const props = defineProps({
  eventDetail: { type: Object, required: true }
})

// 静态资源 URL（不再使用本地兜底）
const detailBgUrl = ref('')
const detailTimeIconUrl = ref('')
const detailLocationIconUrl = ref('')
const detailUserIconUrl = ref('')

// 组件挂载时读取静态资源配置
onMounted(() => {
  const assets = uni.getStorageSync('staticAssets')
  
  detailBgUrl.value = assets?.detail_bg || ''
  detailTimeIconUrl.value = assets?.detail_icon_time || ''
  detailLocationIconUrl.value = assets?.detail_icon_location || ''
  detailUserIconUrl.value = assets?.detail_icon_user || ''
})

const localEvent = computed(() => props.eventDetail || {})

// formatEventLocation 函数已从 @/utils/location.js 导入

/**
 * 计算报名状态，与后端RegistrationStatus.calculateStatus逻辑保持一致
 * 0: 即将开始 (当前时间 < 报名开始时间)
 * 1: 报名中 (报名开始时间 <= 当前时间 <= 报名结束时间)  
 * 2: 报名截止 (当前时间 > 报名结束时间)
 */
const calculatedRegistrationStatus = computed(() => {
  const registrationStartTime = localEvent.value?.registrationStartTime
  const registrationEndTime = localEvent.value?.registrationEndTime
  
  try {
    const now = new Date()
    
    // 未开始：报名开始时间不为空 && 当前时间 < 报名开始时间
    if (registrationStartTime && now < new Date(registrationStartTime)) {
      return 0 // 即将开始
    }
    // 报名中：(报名开始时间为空 || 当前时间 >= 报名开始时间) && (报名结束时间为空 || 当前时间 <= 报名结束时间)
    else if ((!registrationStartTime || now >= new Date(registrationStartTime)) && 
             (!registrationEndTime || now <= new Date(registrationEndTime))) {
      return 1 // 报名中
    }
    // 已结束：报名结束时间不为空 && 当前时间 > 报名结束时间
    else if (registrationEndTime && now > new Date(registrationEndTime)) {
      return 2 // 报名截止
    }
    
    // 默认返回报名中（如果时间配置不明确）
    return 1
  } catch (error) {
    console.warn('报名状态计算失败:', error)
    return 1 // 默认显示报名中
  }
})

/**
 * 格式化报名状态文本，与EventCard保持一致
 */
const formatRegistrationStatus = (status) => {
  switch (status) {
    case 0: return '即将开始'
    case 1: return '报名中'
    case 2: return '报名截止'
    default: return '未知'
  }
}

/**
 * 获取报名状态样式类，与EventCard保持一致
 */
const getRegistrationStatusClass = (status) => {
  switch (status) {
    case 0: return 'not-started'  // 未开始
    case 1: return 'open'         // 报名中
    case 2: return 'ended'        // 已截止
    default: return 'unknown'
  }
}

const formatEventTime = computed(() => {
  if (!localEvent.value?.startTime || !localEvent.value?.endTime) {
    return '时间待定'
  }
  try {
    const startTime = formatDate(localEvent.value.startTime, 'YYYY-MM-DD HH:mm')
    const endTime = formatDate(localEvent.value.endTime, 'YYYY-MM-DD HH:mm')
    return `${startTime} 至 ${endTime}`
  } catch (error) {
    console.warn('时间格式化失败:', error)
    return '时间格式错误'
  }
})

const remainingSpotsNodes = computed(() => {
  if (!localEvent.value) {
    return [{ type: 'text', text: '加载中...' }]
  }

  const max = Number(localEvent.value.maxParticipants) || 0
  if (max === 0) {
    return [{ type: 'text', text: '剩余名额: 不限人数' }]
  }

  const registered = Number(localEvent.value.registeredCount) || 0
  const remaining = Math.max(0, max - registered)

  return [
    {
      type: 'node',
      name: 'span',
      children: [
        { type: 'text', text: '剩余名额: ' },
        {
          type: 'node',
          name: 'span',
          attrs: { style: 'color: #023F98;' },
          children: [{ type: 'text', text: String(remaining) }]
        },
        { type: 'text', text: `/${max}` }
      ]
    }
  ]
})
</script>

<style lang="scss" scoped>
.info-card {
  background: #FFFFFF;
  margin: 0 30rpx;
  margin-top: -60rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2,63,152,0.1);
  padding: 30rpx;
  position: relative;
  z-index: 1;
}

.info-icon {
  width: 32rpx;
  height: 32rpx;
}

.card-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.status-tag-detail {
  width: 90rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  font-size: 22rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
  
  // 与EventCard保持一致的状态样式
  &.ended {
    background: #9B9A9A;
    color: #FFFFFF;
  }
  &.ended .status-bg-image {
    display: none;
  }
  
  &.not-started {
    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
    color: #23232A;
  }
  &.not-started .status-bg-image {
    display: none;
  }
  
  &.open {
    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
    color: #23232A;
  }
  &.open .status-bg-image {
    display: none;
  }
}

.event-title-section {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: flex-start;
}

.event-title {
  white-space: normal;
  word-break: break-word;
  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
  font-weight: normal;
  font-size: 32rpx;
  color: #23232A;
  line-height: 1.5;
}

.info-row {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
}

.info-text {
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-size: 26rpx;
  color: #606266;
  margin-left: 16rpx;
}

.status-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.status-text {
  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
  font-weight: normal;
  font-size: 22rpx;
  color: #023F98;
  position: relative;
  z-index: 2;
  
  // 根据父级状态调整文字颜色
  .status-tag-detail.ended & {
    color: #FFFFFF;
  }
  
  .status-tag-detail.not-started & {
    color: #23232A;
  }
  
  .status-tag-detail.open & {
    color: #23232A;
  }
}
</style>

