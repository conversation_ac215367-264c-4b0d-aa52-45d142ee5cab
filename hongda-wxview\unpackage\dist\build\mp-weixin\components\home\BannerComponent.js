"use strict";const e=require("../../common/vendor.js"),a=require("../../api/platform/ad.js"),t=require("../../utils/navigation.js"),r=require("../../utils/image.js");if(!Array){e.resolveComponent("up-swiper")()}Math;const o={__name:"BannerComponent",setup(o){const i=e.ref([]),n=e.computed((()=>i.value.map((e=>({...e,url:r.getFullImageUrl(e.imageUrl)}))))),l=e=>{const a=i.value[e];a&&t.navigateTo(a)};return e.onMounted((()=>{(async()=>{try{const e=await a.getAdListByPositionApi("HOME_BANNER");e.data&&(i.value=e.data)}catch(e){console.error("获取轮播图失败:",e)}})()})),(a,t)=>e.e({a:n.value.length>0},n.value.length>0?{b:e.o(l),c:e.p({list:n.value,keyName:"url",circular:!0,autoplay:!0,interval:3e3,duration:500,indicator:!0,indicatorActiveColor:"#FFFFFF",indicatorInactiveColor:"rgba(255, 255, 255, 0.5)",indicatorMode:"dot",imgMode:"aspectFill",height:"296rpx",radius:"16rpx"})}:{})}},i=e._export_sfc(o,[["__scopeId","data-v-4668000c"]]);wx.createComponent(i);
