<view class="page-container data-v-6f95dcba"><view class="custom-header data-v-6f95dcba"><view class="status-bar data-v-6f95dcba"></view><view class="nav-bar data-v-6f95dcba" style="{{c}}"><u-icon wx:if="{{b}}" class="data-v-6f95dcba" bindclick="{{a}}" u-i="6f95dcba-0" bind:__l="__l" u-p="{{b}}"></u-icon><view class="page-title-container data-v-6f95dcba"><text class="page-title data-v-6f95dcba">红大出海</text></view></view><view class="search-section data-v-6f95dcba"><view class="search-input-wrapper data-v-6f95dcba"><u-icon wx:if="{{d}}" class="data-v-6f95dcba" u-i="6f95dcba-1" bind:__l="__l" u-p="{{d}}"></u-icon><input class="search-input data-v-6f95dcba" placeholder="搜索活动、资讯" confirm-type="search" bindconfirm="{{e}}" value="{{f}}" bindinput="{{g}}"/><u-icon wx:if="{{h}}" class="data-v-6f95dcba" bindclick="{{i}}" u-i="6f95dcba-2" bind:__l="__l" u-p="{{j}}"></u-icon></view><text wx:if="{{k}}" class="cancel-btn data-v-6f95dcba" bindtap="{{l}}">取消</text></view></view><view class="tabs-container data-v-6f95dcba"><view class="custom-tabs data-v-6f95dcba"><view wx:for="{{m}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', 'data-v-6f95dcba', tab.c && 'active']}}" bindtap="{{tab.d}}" style="{{tab.e}}">{{tab.a}}</view></view></view><scroll-view scroll-y class="result-list-scroll data-v-6f95dcba" bindscrolltolower="{{z}}"><view wx:if="{{n}}" class="loading-container data-v-6f95dcba"><u-loading-icon wx:if="{{o}}" class="data-v-6f95dcba" u-i="6f95dcba-3" bind:__l="__l" u-p="{{o}}"></u-loading-icon></view><view wx:else class="data-v-6f95dcba"><view wx:if="{{p}}" class="empty-state-container data-v-6f95dcba"><u-empty wx:if="{{q}}" class="data-v-6f95dcba" u-i="6f95dcba-4" bind:__l="__l" u-p="{{r}}"></u-empty><u-empty wx:else class="data-v-6f95dcba" u-i="6f95dcba-5" bind:__l="__l" u-p="{{s||''}}"></u-empty></view><view wx:else class="result-list data-v-6f95dcba"><view wx:for="{{t}}" wx:for-item="item" wx:key="m" class="result-card data-v-6f95dcba" bindtap="{{item.n}}"><view class="card-cover data-v-6f95dcba"><image class="cover-image data-v-6f95dcba" src="{{item.a}}" mode="aspectFill"></image><view wx:if="{{item.b}}" class="{{['status-badge', 'data-v-6f95dcba', item.d]}}">{{item.c}}</view></view><view class="card-content data-v-6f95dcba"><text class="card-title data-v-6f95dcba">{{item.e}}</text><view class="card-meta data-v-6f95dcba"><u-icon wx:if="{{v}}" class="data-v-6f95dcba" u-i="{{item.f}}" bind:__l="__l" u-p="{{v}}"></u-icon><text class="meta-text data-v-6f95dcba">{{item.g}}</text></view><view class="card-meta data-v-6f95dcba"><u-icon wx:if="{{w}}" class="data-v-6f95dcba" u-i="{{item.h}}" bind:__l="__l" u-p="{{w}}"></u-icon><text class="meta-text data-v-6f95dcba">{{item.i}}</text></view><view class="card-meta data-v-6f95dcba"><u-icon wx:if="{{x}}" class="data-v-6f95dcba" u-i="{{item.j}}" bind:__l="__l" u-p="{{x}}"></u-icon><text class="meta-text data-v-6f95dcba">{{item.k}}: {{item.l}}</text></view></view></view><u-loadmore wx:if="{{y}}" class="data-v-6f95dcba" u-i="6f95dcba-9" bind:__l="__l" u-p="{{y}}"/></view></view></scroll-view></view>