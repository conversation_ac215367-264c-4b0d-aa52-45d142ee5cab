"use strict";
require("./config.js");
function calculateRemainingSpots(maxParticipants, registeredCount) {
  if (!maxParticipants || maxParticipants <= 0) {
    return "不限";
  }
  const remaining = maxParticipants - (registeredCount || 0);
  return remaining > 0 ? remaining : 0;
}
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
exports.calculateRemainingSpots = calculateRemainingSpots;
exports.debounce = debounce;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/tools.js.map
