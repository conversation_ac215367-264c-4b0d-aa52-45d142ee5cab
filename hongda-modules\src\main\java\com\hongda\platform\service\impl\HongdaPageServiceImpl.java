package com.hongda.platform.service.impl;

import com.hongda.common.utils.DateUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.platform.domain.HongdaPage;
import com.hongda.platform.mapper.HongdaPageMapper;
import com.hongda.platform.service.IHongdaPageService;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 页面管理Service业务层处理
 * <AUTHOR>
 * @date 2025-08-15
 */
@Service
public class HongdaPageServiceImpl implements IHongdaPageService
{
    @Autowired
    private HongdaPageMapper hongdaPageMapper;

    // [移除] 不再需要注入OSS服务，URL转换由AOP切面统一处理
    // @Autowired
    // private OssFileStorageService ossFileStorageService;

    // [移除] 不再需要注入URL过期时间
    // @Value("${hongda.oss.url-expire-minutes}")
    // private int urlExpireMinutes;

    /**
     * 查询页面管理
     */
    @Override
    public HongdaPage selectHongdaPageById(Long id)
    {
        HongdaPage page = hongdaPageMapper.selectHongdaPageById(id);
        // [移除] 不再需要手动处理HTML内容加载，AOP切面会自动处理
        // if (page != null) {
        //     page.setContent(processHtmlOnLoad(page.getContent()));
        // }
        return page;
    }

    /**
     * 查询页面管理列表
     */
    @Override
    public List<HongdaPage> selectHongdaPageList(HongdaPage hongdaPage)
    {
        // [移除] 不再需要遍历处理富文本字段
        return hongdaPageMapper.selectHongdaPageList(hongdaPage);
    }

    /**
     * 新增页面管理
     */
    @Override
    public int insertHongdaPage(HongdaPage hongdaPage)
    {
        hongdaPage.setCreateTime(DateUtils.getNowDate());
        // [保留] 保存前处理富文本字段是必要的
        hongdaPage.setContent(processHtmlOnSave(hongdaPage.getContent()));
        return hongdaPageMapper.insertHongdaPage(hongdaPage);
    }

    /**
     * 修改页面管理
     */
    @Override
    public int updateHongdaPage(HongdaPage hongdaPage)
    {
        hongdaPage.setUpdateTime(DateUtils.getNowDate());
        // [保留] 更新前处理富文本字段是必要的
        hongdaPage.setContent(processHtmlOnSave(hongdaPage.getContent()));
        return hongdaPageMapper.updateHongdaPage(hongdaPage);
    }

    /**
     * 批量删除页面管理
     */
    @Override
    public int deleteHongdaPageByIds(Long[] ids)
    {
        return hongdaPageMapper.deleteHongdaPageByIds(ids);
    }

    /**
     * 删除页面管理信息
     */
    @Override
    public int deleteHongdaPageById(Long id)
    {
        return hongdaPageMapper.deleteHongdaPageById(id);
    }

    /**
     * [保留] 这个方法在保存时处理HTML，将临时URL转换为objectName，是正确且必要的。
     */
    private String processHtmlOnSave(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return htmlContent;
        }
        Document doc = Jsoup.parseBodyFragment(htmlContent);
        Elements images = doc.select("img[data-href^=oss-object-name://]");
        for (Element img : images) {
            String dataHref = img.attr("data-href");
            String objectName = dataHref.substring("oss-object-name://".length());
            img.attr("data-oss-object-name", objectName);
            img.removeAttr("src");
            img.removeAttr("data-href");
        }
        return doc.body().html();
    }

    // [移除] processHtmlOnLoad 方法已不再需要
}