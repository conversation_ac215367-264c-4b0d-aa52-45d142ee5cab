<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.platform.mapper.HongdaNavMapper">

    <resultMap type="com.hongda.platform.domain.HongdaNav" id="HongdaNavResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="iconUrl" column="icon_url"/>
        <result property="linkType" column="link_type"/>
        <result property="linkTarget" column="link_target"/>
        <result property="positionCode" column="position_code"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="linkTargetTitle" column="link_target_title"/>
    </resultMap>

    <sql id="selectHongdaNavVo">
        select id, title, icon_url, link_type, link_target, position_code, sort_order, status, create_by, create_time, update_by, update_time from hongda_nav
    </sql>

    <select id="selectHongdaNavList" parameterType="com.hongda.platform.domain.HongdaNav" resultMap="HongdaNavResult">
        SELECT
        n.id,
        n.title,
        n.icon_url,
        n.link_type,
        n.link_target,
        n.position_code,
        n.sort_order,
        n.status,
        -- [核心修改] 增加了一个 page_from_url 的 LEFT JOIN，并扩充了 CASE 语句
        CASE
        WHEN n.link_type = 'ARTICLE' THEN art.title
        WHEN n.link_type = 'EVENT'   THEN eve.title
        WHEN n.link_type = 'COUNTRY' THEN cou.name_cn
        WHEN n.link_type = 'PARK'    THEN par.name
        WHEN n.link_type = 'CUSTOM_PAGE' THEN page_from_id.title
        -- 新增：当类型是内部页面时，也尝试通过URL去匹配页面标题
        WHEN n.link_type = 'PAGE'    THEN page_from_url.title
        ELSE NULL
        END AS link_target_title
        FROM
        hongda_nav n
        LEFT JOIN hongda_article art ON n.link_type = 'ARTICLE' AND n.link_target = CAST(art.id AS CHAR)
        LEFT JOIN hongda_event eve   ON n.link_type = 'EVENT'   AND n.link_target = CAST(eve.id AS CHAR)
        LEFT JOIN hongda_country cou ON n.link_type = 'COUNTRY' AND n.link_target = CAST(cou.id AS CHAR)
        LEFT JOIN hongda_park par    ON n.link_type = 'PARK'    AND n.link_target = CAST(par.id AS CHAR)
        -- 这个JOIN用于通过ID匹配自定义页面
        LEFT JOIN hongda_page page_from_id   ON n.link_type = 'CUSTOM_PAGE' AND n.link_target = CAST(page_from_id.id AS CHAR)
        -- [新增] 这个JOIN用于通过URL路径匹配内部页面
        LEFT JOIN hongda_page page_from_url  ON n.link_type = 'PAGE' AND n.link_target = page_from_url.target_url
        <where>
            <if test="title != null and title != ''"> and n.title like concat('%', #{title}, '%')</if>
            <if test="positionCode != null and positionCode != ''"> and n.position_code = #{positionCode}</if>
            <if test="status != null"> and n.status = #{status}</if>
        </where>
        order by n.sort_order asc
    </select>

    <select id="selectHongdaNavById" parameterType="Long" resultMap="HongdaNavResult">
        <include refid="selectHongdaNavVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaNav" parameterType="com.hongda.platform.domain.HongdaNav" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_nav
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="iconUrl != null and iconUrl != ''">icon_url,</if>
            <if test="linkType != null and linkType != ''">link_type,</if>
            <if test="linkTarget != null">link_target,</if>
            <if test="positionCode != null and positionCode != ''">position_code,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="iconUrl != null and iconUrl != ''">#{iconUrl},</if>
            <if test="linkType != null and linkType != ''">#{linkType},</if>
            <if test="linkTarget != null">#{linkTarget},</if>
            <if test="positionCode != null and positionCode != ''">#{positionCode},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateHongdaNav" parameterType="com.hongda.platform.domain.HongdaNav">
        update hongda_nav
        <set>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="linkType != null">link_type = #{linkType},</if>
            <if test="linkTarget != null">link_target = #{linkTarget},</if>
            <if test="positionCode != null and positionCode != ''">position_code = #{positionCode},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteHongdaNavById" parameterType="Long">
        delete from hongda_nav where id = #{id}
    </delete>

    <delete id="deleteHongdaNavByIds" parameterType="String">
        delete from hongda_nav where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>