<view class="event-list-page data-v-1d529312"><view class="header-wrapper data-v-1d529312"><image class="header-bg data-v-1d529312" src="{{a}}" mode="aspectFill"></image><view class="custom-navbar data-v-1d529312"><view class="navbar-left data-v-1d529312" bindtap="{{c}}"><up-icon wx:if="{{b}}" class="data-v-1d529312" u-i="1d529312-0" bind:__l="__l" u-p="{{b}}"></up-icon></view><view class="navbar-title data-v-1d529312"><text class="title-text data-v-1d529312">热门活动列表</text></view><view class="navbar-right data-v-1d529312"></view></view><view class="top-controls data-v-1d529312"><up-subsection wx:if="{{e}}" class="data-v-1d529312" bindchange="{{d}}" u-i="1d529312-1" bind:__l="__l" u-p="{{e}}"></up-subsection><view class="search-wrapper data-v-1d529312"><custom-search-box wx:if="{{i}}" class="data-v-1d529312" bindsearch="{{f}}" bindinput="{{g}}" u-i="1d529312-2" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"></custom-search-box></view></view></view><view wx:if="{{j}}" class="filter-bar sticky-filter-bar data-v-1d529312"><view class="filter-main-buttons data-v-1d529312"><view class="filter-button data-v-1d529312" bindtap="{{n}}"><text class="filter-text data-v-1d529312">{{k}}</text><up-icon wx:if="{{m}}" class="{{['data-v-1d529312', l && 'rotate-180']}}" u-i="1d529312-3" bind:__l="__l" u-p="{{m}}"></up-icon></view><view class="filter-button data-v-1d529312" bindtap="{{r}}"><text class="filter-text data-v-1d529312">{{o}}</text><up-icon wx:if="{{q}}" class="{{['data-v-1d529312', p && 'rotate-180']}}" u-i="1d529312-4" bind:__l="__l" u-p="{{q}}"></up-icon></view><view class="filter-button data-v-1d529312" bindtap="{{w}}"><text class="filter-text data-v-1d529312">{{s}}</text><up-icon wx:if="{{v}}" class="{{['data-v-1d529312', t && 'rotate-180']}}" u-i="1d529312-5" bind:__l="__l" u-p="{{v}}"></up-icon></view><view class="filter-button data-v-1d529312" bindtap="{{A}}"><text class="filter-text data-v-1d529312">{{x}}</text><up-icon wx:if="{{z}}" class="{{['data-v-1d529312', y && 'rotate-180']}}" u-i="1d529312-6" bind:__l="__l" u-p="{{z}}"></up-icon></view></view><view wx:if="{{B}}" class="filter-panel data-v-1d529312"><text class="section-title data-v-1d529312">排序</text><view class="option-grid data-v-1d529312"><view wx:for="{{C}}" wx:for-item="option" wx:key="b" class="{{['data-v-1d529312', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-1d529312">{{option.a}}</text></view></view><view class="filter-buttons data-v-1d529312"><view class="filter-btn reset-btn data-v-1d529312" bindtap="{{D}}"><text class="btn-text data-v-1d529312">重置</text></view><view class="filter-btn complete-btn data-v-1d529312" bindtap="{{E}}"><text class="btn-text data-v-1d529312">完成</text></view></view></view><view wx:if="{{F}}" class="filter-panel data-v-1d529312"><view class="option-grid data-v-1d529312" style="margin-bottom:20rpx"><view class="{{['data-v-1d529312', 'option-item', H]}}" bindtap="{{I}}"><text class="option-text data-v-1d529312">{{G}}</text></view></view><text class="section-title data-v-1d529312">热门地区</text><view class="option-grid data-v-1d529312"><view wx:for="{{J}}" wx:for-item="option" wx:key="b" class="{{['data-v-1d529312', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-1d529312">{{option.a}}</text></view></view><view wx:if="{{K}}" class="data-v-1d529312" style="margin-top:20rpx"><text class="section-title data-v-1d529312">其他地区</text><view class="option-grid data-v-1d529312"><view wx:for="{{L}}" wx:for-item="city" wx:key="b" class="{{['data-v-1d529312', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-1d529312">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-1d529312"><view class="filter-btn reset-btn data-v-1d529312" bindtap="{{M}}"><text class="btn-text data-v-1d529312">重置</text></view><view class="filter-btn complete-btn data-v-1d529312" bindtap="{{N}}"><text class="btn-text data-v-1d529312">完成</text></view></view></view><view wx:if="{{O}}" class="filter-panel data-v-1d529312"><text class="section-title data-v-1d529312">时间</text><view class="option-grid data-v-1d529312"><view wx:for="{{P}}" wx:for-item="option" wx:key="b" class="{{['data-v-1d529312', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-1d529312">{{option.a}}</text></view></view><view class="filter-buttons data-v-1d529312"><view class="filter-btn reset-btn data-v-1d529312" bindtap="{{Q}}"><text class="btn-text data-v-1d529312">重置</text></view><view class="filter-btn complete-btn data-v-1d529312" bindtap="{{R}}"><text class="btn-text data-v-1d529312">完成</text></view></view></view><view wx:if="{{S}}" class="filter-panel data-v-1d529312"><text class="section-title data-v-1d529312">全部状态</text><view class="option-grid data-v-1d529312"><view wx:for="{{T}}" wx:for-item="option" wx:key="b" class="{{['data-v-1d529312', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-1d529312">{{option.a}}</text></view></view><view class="filter-buttons data-v-1d529312"><view class="filter-btn reset-btn data-v-1d529312" bindtap="{{U}}"><text class="btn-text data-v-1d529312">重置</text></view><view class="filter-btn complete-btn data-v-1d529312" bindtap="{{V}}"><text class="btn-text data-v-1d529312">完成</text></view></view></view></view><scroll-view wx:if="{{W}}" scroll-y class="event-list-scroll list-scroll-with-filter data-v-1d529312" bindscrolltolower="{{ae}}" refresher-enabled refresher-triggered="{{af}}" bindrefresherrefresh="{{ag}}"><view wx:if="{{X}}" class="empty-state data-v-1d529312"><up-empty wx:if="{{Y}}" class="data-v-1d529312" u-i="1d529312-7" bind:__l="__l" u-p="{{Y}}"></up-empty><view wx:if="{{Z}}" class="retry-container data-v-1d529312"><up-button wx:if="{{ab}}" class="data-v-1d529312" u-s="{{['d']}}" bindclick="{{aa}}" u-i="1d529312-8" bind:__l="__l" u-p="{{ab}}"> 重新加载 </up-button></view></view><event-card wx:for="{{ac}}" wx:for-item="event" wx:key="a" class="data-v-1d529312" bindclick="{{event.b}}" u-i="{{event.c}}" bind:__l="__l" u-p="{{event.d}}"/><view class="loadmore-wrapper data-v-1d529312"><up-loadmore wx:if="{{ad}}" class="data-v-1d529312" u-i="1d529312-10" bind:__l="__l" u-p="{{ad}}"/></view></scroll-view><view wx:if="{{ah}}" class="filter-bar calendar-filter-bar sticky-filter-bar data-v-1d529312"><view class="filter-main-buttons data-v-1d529312"><view class="filter-button data-v-1d529312" bindtap="{{al}}"><text class="filter-text data-v-1d529312">{{ai}}</text><up-icon wx:if="{{ak}}" class="{{['data-v-1d529312', aj && 'rotate-180']}}" u-i="1d529312-11" bind:__l="__l" u-p="{{ak}}"></up-icon></view><view class="filter-button data-v-1d529312" bindtap="{{ap}}"><text class="filter-text data-v-1d529312">{{am}}</text><up-icon wx:if="{{ao}}" class="{{['data-v-1d529312', an && 'rotate-180']}}" u-i="1d529312-12" bind:__l="__l" u-p="{{ao}}"></up-icon></view><view class="filter-button filter-placeholder data-v-1d529312"><text class="filter-text data-v-1d529312"></text></view><view class="filter-button filter-placeholder data-v-1d529312"><text class="filter-text data-v-1d529312"></text></view></view><view wx:if="{{aq}}" class="filter-panel data-v-1d529312"><view class="option-grid data-v-1d529312" style="margin-bottom:20rpx"><view class="{{['data-v-1d529312', 'option-item', as]}}" bindtap="{{at}}"><text class="option-text data-v-1d529312">{{ar}}</text></view></view><text class="section-title data-v-1d529312">热门地区</text><view class="option-grid data-v-1d529312"><view wx:for="{{av}}" wx:for-item="option" wx:key="b" class="{{['data-v-1d529312', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-1d529312">{{option.a}}</text></view></view><view wx:if="{{aw}}" class="data-v-1d529312" style="margin-top:20rpx"><text class="section-title data-v-1d529312">其他地区</text><view class="option-grid data-v-1d529312"><view wx:for="{{ax}}" wx:for-item="city" wx:key="b" class="{{['data-v-1d529312', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-1d529312">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-1d529312"><view class="filter-btn reset-btn data-v-1d529312" bindtap="{{ay}}"><text class="btn-text data-v-1d529312">重置</text></view><view class="filter-btn complete-btn data-v-1d529312" bindtap="{{az}}"><text class="btn-text data-v-1d529312">完成</text></view></view></view><view wx:if="{{aA}}" class="filter-panel data-v-1d529312"><text class="section-title data-v-1d529312">时间</text><view class="option-grid data-v-1d529312"><view wx:for="{{aB}}" wx:for-item="option" wx:key="b" class="{{['data-v-1d529312', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-1d529312">{{option.a}}</text></view></view><view class="filter-buttons data-v-1d529312"><view class="filter-btn reset-btn data-v-1d529312" bindtap="{{aC}}"><text class="btn-text data-v-1d529312">重置</text></view><view class="filter-btn complete-btn data-v-1d529312" bindtap="{{aD}}"><text class="btn-text data-v-1d529312">完成</text></view></view></view></view><scroll-view wx:if="{{aE}}" scroll-y class="event-list-scroll calendar-view calendar-scroll-with-filter data-v-1d529312" bindscrolltolower="{{aN}}" refresher-enabled refresher-triggered="{{aO}}" bindrefresherrefresh="{{aP}}"><view wx:if="{{aF}}" class="empty-state data-v-1d529312"><up-empty wx:if="{{aG}}" class="data-v-1d529312" u-i="1d529312-13" bind:__l="__l" u-p="{{aG}}"></up-empty><view wx:if="{{aH}}" class="retry-container data-v-1d529312"><up-button wx:if="{{aJ}}" class="data-v-1d529312" u-s="{{['d']}}" bindclick="{{aI}}" u-i="1d529312-14" bind:__l="__l" u-p="{{aJ}}"> 重新加载 </up-button></view></view><event-calendar-timeline wx:else class="data-v-1d529312" bindclickItem="{{aK}}" bindviewMore="{{aL}}" u-i="1d529312-15" bind:__l="__l" u-p="{{aM||''}}"/><view class="calendar-bottom-safe data-v-1d529312"/></scroll-view><custom-tab-bar wx:if="{{aQ}}" class="data-v-1d529312" u-i="1d529312-16" bind:__l="__l" u-p="{{aQ}}"/></view>