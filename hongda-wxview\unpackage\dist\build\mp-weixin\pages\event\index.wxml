<view class="event-list-page data-v-fce3c2f9"><view class="header-wrapper data-v-fce3c2f9"><image class="header-bg data-v-fce3c2f9" src="{{a}}" mode="aspectFill"></image><view class="custom-navbar data-v-fce3c2f9"><view class="navbar-title data-v-fce3c2f9"><text class="title-text data-v-fce3c2f9">热门活动列表</text></view></view><view class="top-controls data-v-fce3c2f9"><up-subsection wx:if="{{c}}" class="data-v-fce3c2f9" bindchange="{{b}}" u-i="fce3c2f9-0" bind:__l="__l" u-p="{{c}}"></up-subsection><view class="search-wrapper data-v-fce3c2f9"><custom-search-box wx:if="{{g}}" class="data-v-fce3c2f9" bindsearch="{{d}}" bindinput="{{e}}" u-i="fce3c2f9-1" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"></custom-search-box></view></view></view><view wx:if="{{h}}" class="filter-bar sticky-filter-bar data-v-fce3c2f9"><view class="filter-main-buttons data-v-fce3c2f9"><view class="filter-button data-v-fce3c2f9" bindtap="{{l}}"><text class="filter-text data-v-fce3c2f9">{{i}}</text><up-icon wx:if="{{k}}" class="{{['data-v-fce3c2f9', j && 'rotate-180']}}" u-i="fce3c2f9-2" bind:__l="__l" u-p="{{k}}"></up-icon></view><view class="filter-button data-v-fce3c2f9" bindtap="{{p}}"><text class="filter-text data-v-fce3c2f9">{{m}}</text><up-icon wx:if="{{o}}" class="{{['data-v-fce3c2f9', n && 'rotate-180']}}" u-i="fce3c2f9-3" bind:__l="__l" u-p="{{o}}"></up-icon></view><view class="filter-button data-v-fce3c2f9" bindtap="{{t}}"><text class="filter-text data-v-fce3c2f9">{{q}}</text><up-icon wx:if="{{s}}" class="{{['data-v-fce3c2f9', r && 'rotate-180']}}" u-i="fce3c2f9-4" bind:__l="__l" u-p="{{s}}"></up-icon></view><view class="filter-button data-v-fce3c2f9" bindtap="{{y}}"><text class="filter-text data-v-fce3c2f9">{{v}}</text><up-icon wx:if="{{x}}" class="{{['data-v-fce3c2f9', w && 'rotate-180']}}" u-i="fce3c2f9-5" bind:__l="__l" u-p="{{x}}"></up-icon></view></view><view wx:if="{{z}}" class="filter-panel data-v-fce3c2f9"><text class="section-title data-v-fce3c2f9">排序</text><view class="option-grid data-v-fce3c2f9"><view wx:for="{{A}}" wx:for-item="option" wx:key="b" class="{{['data-v-fce3c2f9', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-fce3c2f9">{{option.a}}</text></view></view><view class="filter-buttons data-v-fce3c2f9"><view class="filter-btn reset-btn data-v-fce3c2f9" bindtap="{{B}}"><text class="btn-text data-v-fce3c2f9">重置</text></view><view class="filter-btn complete-btn data-v-fce3c2f9" bindtap="{{C}}"><text class="btn-text data-v-fce3c2f9">完成</text></view></view></view><view wx:if="{{D}}" class="filter-panel data-v-fce3c2f9"><view class="option-grid data-v-fce3c2f9" style="margin-bottom:20rpx"><view class="{{['data-v-fce3c2f9', 'option-item', F]}}" bindtap="{{G}}"><text class="option-text data-v-fce3c2f9">{{E}}</text></view></view><text class="section-title data-v-fce3c2f9">热门地区</text><view class="option-grid data-v-fce3c2f9"><view wx:for="{{H}}" wx:for-item="option" wx:key="b" class="{{['data-v-fce3c2f9', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-fce3c2f9">{{option.a}}</text></view></view><view wx:if="{{I}}" class="data-v-fce3c2f9" style="margin-top:20rpx"><text class="section-title data-v-fce3c2f9">其他地区</text><view class="option-grid data-v-fce3c2f9"><view wx:for="{{J}}" wx:for-item="city" wx:key="b" class="{{['data-v-fce3c2f9', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-fce3c2f9">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-fce3c2f9"><view class="filter-btn reset-btn data-v-fce3c2f9" bindtap="{{K}}"><text class="btn-text data-v-fce3c2f9">重置</text></view><view class="filter-btn complete-btn data-v-fce3c2f9" bindtap="{{L}}"><text class="btn-text data-v-fce3c2f9">完成</text></view></view></view><view wx:if="{{M}}" class="filter-panel data-v-fce3c2f9"><text class="section-title data-v-fce3c2f9">时间</text><view class="option-grid data-v-fce3c2f9"><view wx:for="{{N}}" wx:for-item="option" wx:key="b" class="{{['data-v-fce3c2f9', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-fce3c2f9">{{option.a}}</text></view></view><view class="filter-buttons data-v-fce3c2f9"><view class="filter-btn reset-btn data-v-fce3c2f9" bindtap="{{O}}"><text class="btn-text data-v-fce3c2f9">重置</text></view><view class="filter-btn complete-btn data-v-fce3c2f9" bindtap="{{P}}"><text class="btn-text data-v-fce3c2f9">完成</text></view></view></view><view wx:if="{{Q}}" class="filter-panel data-v-fce3c2f9"><text class="section-title data-v-fce3c2f9">全部状态</text><view class="option-grid data-v-fce3c2f9"><view wx:for="{{R}}" wx:for-item="option" wx:key="b" class="{{['data-v-fce3c2f9', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-fce3c2f9">{{option.a}}</text></view></view><view class="filter-buttons data-v-fce3c2f9"><view class="filter-btn reset-btn data-v-fce3c2f9" bindtap="{{S}}"><text class="btn-text data-v-fce3c2f9">重置</text></view><view class="filter-btn complete-btn data-v-fce3c2f9" bindtap="{{T}}"><text class="btn-text data-v-fce3c2f9">完成</text></view></view></view></view><scroll-view wx:if="{{U}}" scroll-y class="event-list-scroll list-scroll-with-filter data-v-fce3c2f9" bindscrolltolower="{{ac}}" refresher-enabled refresher-triggered="{{ad}}" bindrefresherrefresh="{{ae}}"><view wx:if="{{V}}" class="empty-state data-v-fce3c2f9"><up-empty wx:if="{{W}}" class="data-v-fce3c2f9" u-i="fce3c2f9-6" bind:__l="__l" u-p="{{W}}"></up-empty><view wx:if="{{X}}" class="retry-container data-v-fce3c2f9"><up-button wx:if="{{Z}}" class="data-v-fce3c2f9" u-s="{{['d']}}" bindclick="{{Y}}" u-i="fce3c2f9-7" bind:__l="__l" u-p="{{Z}}"> 重新加载 </up-button></view></view><event-card wx:for="{{aa}}" wx:for-item="event" wx:key="a" class="data-v-fce3c2f9" bindclick="{{event.b}}" u-i="{{event.c}}" bind:__l="__l" u-p="{{event.d}}"/><view class="loadmore-wrapper data-v-fce3c2f9"><up-loadmore wx:if="{{ab}}" class="data-v-fce3c2f9" u-i="fce3c2f9-9" bind:__l="__l" u-p="{{ab}}"/></view></scroll-view><view wx:if="{{af}}" class="filter-bar calendar-filter-bar sticky-filter-bar data-v-fce3c2f9"><view class="filter-main-buttons data-v-fce3c2f9"><view class="filter-button data-v-fce3c2f9" bindtap="{{aj}}"><text class="filter-text data-v-fce3c2f9">{{ag}}</text><up-icon wx:if="{{ai}}" class="{{['data-v-fce3c2f9', ah && 'rotate-180']}}" u-i="fce3c2f9-10" bind:__l="__l" u-p="{{ai}}"></up-icon></view><view class="filter-button data-v-fce3c2f9" bindtap="{{an}}"><text class="filter-text data-v-fce3c2f9">{{ak}}</text><up-icon wx:if="{{am}}" class="{{['data-v-fce3c2f9', al && 'rotate-180']}}" u-i="fce3c2f9-11" bind:__l="__l" u-p="{{am}}"></up-icon></view><view class="filter-button filter-placeholder data-v-fce3c2f9"><text class="filter-text data-v-fce3c2f9"></text></view><view class="filter-button filter-placeholder data-v-fce3c2f9"><text class="filter-text data-v-fce3c2f9"></text></view></view><view wx:if="{{ao}}" class="filter-panel data-v-fce3c2f9"><view class="option-grid data-v-fce3c2f9" style="margin-bottom:20rpx"><view class="{{['data-v-fce3c2f9', 'option-item', aq]}}" bindtap="{{ar}}"><text class="option-text data-v-fce3c2f9">{{ap}}</text></view></view><text class="section-title data-v-fce3c2f9">热门地区</text><view class="option-grid data-v-fce3c2f9"><view wx:for="{{as}}" wx:for-item="option" wx:key="b" class="{{['data-v-fce3c2f9', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-fce3c2f9">{{option.a}}</text></view></view><view wx:if="{{at}}" class="data-v-fce3c2f9" style="margin-top:20rpx"><text class="section-title data-v-fce3c2f9">其他地区</text><view class="option-grid data-v-fce3c2f9"><view wx:for="{{av}}" wx:for-item="city" wx:key="b" class="{{['data-v-fce3c2f9', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-fce3c2f9">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-fce3c2f9"><view class="filter-btn reset-btn data-v-fce3c2f9" bindtap="{{aw}}"><text class="btn-text data-v-fce3c2f9">重置</text></view><view class="filter-btn complete-btn data-v-fce3c2f9" bindtap="{{ax}}"><text class="btn-text data-v-fce3c2f9">完成</text></view></view></view><view wx:if="{{ay}}" class="filter-panel data-v-fce3c2f9"><text class="section-title data-v-fce3c2f9">时间</text><view class="option-grid data-v-fce3c2f9"><view wx:for="{{az}}" wx:for-item="option" wx:key="b" class="{{['data-v-fce3c2f9', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-fce3c2f9">{{option.a}}</text></view></view><view class="filter-buttons data-v-fce3c2f9"><view class="filter-btn reset-btn data-v-fce3c2f9" bindtap="{{aA}}"><text class="btn-text data-v-fce3c2f9">重置</text></view><view class="filter-btn complete-btn data-v-fce3c2f9" bindtap="{{aB}}"><text class="btn-text data-v-fce3c2f9">完成</text></view></view></view></view><scroll-view wx:if="{{aC}}" scroll-y class="event-list-scroll calendar-view calendar-scroll-with-filter data-v-fce3c2f9" bindscrolltolower="{{aL}}" refresher-enabled refresher-triggered="{{aM}}" bindrefresherrefresh="{{aN}}"><view wx:if="{{aD}}" class="empty-state data-v-fce3c2f9"><up-empty wx:if="{{aE}}" class="data-v-fce3c2f9" u-i="fce3c2f9-12" bind:__l="__l" u-p="{{aE}}"></up-empty><view wx:if="{{aF}}" class="retry-container data-v-fce3c2f9"><up-button wx:if="{{aH}}" class="data-v-fce3c2f9" u-s="{{['d']}}" bindclick="{{aG}}" u-i="fce3c2f9-13" bind:__l="__l" u-p="{{aH}}"> 重新加载 </up-button></view></view><event-calendar-timeline wx:else class="data-v-fce3c2f9" bindclickItem="{{aI}}" bindviewMore="{{aJ}}" u-i="fce3c2f9-14" bind:__l="__l" u-p="{{aK||''}}"/><view class="calendar-bottom-safe data-v-fce3c2f9"/></scroll-view><custom-tab-bar wx:if="{{aO}}" class="data-v-fce3c2f9" u-i="fce3c2f9-15" bind:__l="__l" u-p="{{aO}}"/></view>