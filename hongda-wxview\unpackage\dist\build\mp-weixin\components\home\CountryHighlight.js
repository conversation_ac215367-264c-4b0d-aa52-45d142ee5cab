"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/country.js");if(!Array){(e.resolveComponent("uni-load-more")+e.resolveComponent("uni-icons"))()}Math||((()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js"))();const n={__name:"CountryHighlight",setup(n){const t=e.ref(!0),o=e.ref(!1),i=e.ref([]),u=e.ref(null),l=e.ref(null),c=e.ref("basic"),v=e.ref({}),r=e.ref([{id:"basic",name:"基本信息",iconKey:"icon_tab_basic_normal",activeIconKey:"icon_tab_basic_active"},{id:"investment",name:"招商政策",iconKey:"icon_tab_investment_normal",activeIconKey:"icon_tab_investment_active"},{id:"customs",name:"海关政策",iconKey:"icon_tab_customs_normal",activeIconKey:"icon_tab_customs_active"},{id:"tax",name:"税务政策",iconKey:"icon_tab_tax_normal",activeIconKey:"icon_tab_tax_active"},{id:"parks",name:"工业园区",iconKey:"icon_tab_parks_normal",activeIconKey:"icon_tab_parks_active"}]),s=e.computed((()=>r.value.map((e=>({id:e.id,name:e.name,icon:v.value[e.iconKey]||"",activeIcon:v.value[e.activeIconKey]||""}))))),d=e.computed((()=>({backgroundImage:v.value.bg_badge_gold?`url('${v.value.bg_badge_gold}')`:"none"}))),_=e.computed((()=>({backgroundImage:v.value.bg_badge_blue?`url('${v.value.bg_badge_blue}')`:"none"}))),m=e.computed((()=>({backgroundImage:v.value.bg_tab_active_home?`url('${v.value.bg_tab_active_home}')`:"none"}))),b=e.computed((()=>{var e,a;return`${(null==(e=l.value)?void 0:e.nameCn)||""} - ${(null==(a=s.value.find((e=>e.id===c.value)))?void 0:a.name)||""}`})),g=e.computed((()=>{if(!l.value)return"<p>暂无相关信息。</p>";return{basic:l.value.introduction,investment:l.value.investmentPolicy,customs:l.value.customsPolicy,tax:l.value.taxPolicy,parks:"<p>请点击“更多”查看详细的工业园区列表。</p>"}[c.value]||"<p>暂无相关信息。</p>"})),p=async e=>{o.value=!0,l.value=null;try{const n=await a.getCountryDetail(e);l.value=n.data}catch(n){console.error(`获取ID为 ${e} 的国别详情失败:`,n)}finally{o.value=!1}},y=()=>{u.value&&e.index.navigateTo({url:`/pages_sub/pages_country/detail?id=${u.value}&tab=${c.value}`})};return e.onMounted((()=>{v.value=e.index.getStorageSync("staticAssets")||{},(async()=>{t.value=!0;try{const e={pageNum:1,pageSize:5},n=await a.getCountryList(e);if(n.data&&n.data.length>0){i.value=n.data;const e=n.data[0].id;u.value=e,await p(e)}}catch(e){console.error("获取推荐国别失败:",e)}finally{t.value=!1}})()})),(a,n)=>e.e({a:!t.value&&i.value.length>0},!t.value&&i.value.length>0?e.e({b:e.f(i.value,((a,n,t)=>({a:a.listCoverUrl,b:e.t(a.nameCn),c:u.value===a.id?1:"",d:e.s(u.value===a.id?d.value:_.value),e:a.id,f:u.value===a.id?1:"",g:e.o((e=>{return n=a.id,void(u.value!==n&&(u.value=n,c.value="basic",p(n)));var n}),a.id)}))),c:e.f(s.value,((a,n,t)=>({a:c.value===a.id?a.activeIcon:a.icon,b:e.t(a.name),c:a.id,d:c.value===a.id?1:"",e:e.o((e=>{return n=a.id,void(c.value=n);var n}),a.id),f:e.s(c.value===a.id?m.value:{})}))),d:o.value},o.value?{e:e.p({status:"loading"})}:l.value?{g:e.t(b.value),h:e.p({type:"right",size:"14",color:"#888"}),i:e.o(y),j:g.value}:{},{f:l.value}):{})}},t=e._export_sfc(n,[["__scopeId","data-v-5995617f"]]);wx.createComponent(t);
