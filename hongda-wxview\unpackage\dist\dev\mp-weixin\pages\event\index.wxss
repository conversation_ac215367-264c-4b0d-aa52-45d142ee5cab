/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-8e954d49 {
  height: 100%;
  background-color: #f4f4f4;
}
.event-list-page.data-v-8e954d49 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.header-wrapper.data-v-8e954d49 {
  /* 修改为固定定位，滚动时不移动 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  /* 确保头部在最上层 */
  overflow: hidden;
  min-height: calc(452rpx + var(--status-bar-height));
  /* 新增：底部圆角，使其与下方白色区域过渡更自然 */
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  padding-bottom: 20rpx;
}

/* 背景图片样式 - 完全覆盖状态栏 */
.header-bg.data-v-8e954d49 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  /* 确保图片按比例缩放，避免变形 */
  object-fit: cover;
  /* 图片居中显示 */
  object-position: center;
}

/* 自定义导航栏样式 */
.custom-navbar.data-v-8e954d49 {
  position: absolute;
  top: 94rpx;
  left: 0;
  right: 0;
  width: 750rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  z-index: 2;
  border-radius: 0rpx;
}
.navbar-left.data-v-8e954d49,
.navbar-right.data-v-8e954d49 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title.data-v-8e954d49 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title-text.data-v-8e954d49 {
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-weight: normal;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 44rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

/* 确保内容在背景图片之上 */
.top-controls.data-v-8e954d49,
.filter-bar.data-v-8e954d49 {
  position: relative;
  z-index: 2;
}
.top-controls.data-v-8e954d49 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  gap: 32rpx;
  position: absolute;
  top: 182rpx;
  left: 16rpx;
  right: 24rpx;
  /* 确保子元素完全水平对齐 */
  height: 60rpx;
  /* 调整为适应新的搜索框高度 */
}
.search-wrapper.data-v-8e954d49 {
  width: 446rpx;
  height: 60rpx;
  /* 调整为与新的搜索框高度一致 */
  flex-shrink: 0;
  /* 防止被压缩 */
  display: flex;
  align-items: center;
  /* 确保与up-subsection组件基线对齐 */
  vertical-align: middle;
}
.filter-bar.data-v-8e954d49 {
  position: relative;
  background-color: transparent;
  padding: 0;
  margin-bottom: 10rpx;
}

/* 主筛选按钮容器 */
.filter-main-buttons.data-v-8e954d49 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx;
}

/* 单个筛选按钮 */
.filter-button.data-v-8e954d49 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 8rpx 20rpx;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 0;
}
.filter-text.data-v-8e954d49 {
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-weight: normal;
  font-size: 26rpx;
  color: #66666E;
  line-height: 44rpx;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 箭头旋转动画 */
.rotate-180.data-v-8e954d49 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

/* 筛选面板*/
.filter-panel.data-v-8e954d49 {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  width: 750rpx;
  max-height: 60vh;
  overflow-y: auto;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 32rpx 24rpx;
  margin: 0 auto;
  max-height: 1046rpx;
  overflow-y: auto;
}

/* 💥 新增：通用的筛选栏吸顶样式 */
.sticky-filter-bar.data-v-8e954d49 {
  position: fixed;
  /* 头部蓝色区域的高度，可根据实际情况微调 */
  top: calc(260rpx + var(--status-bar-height));
  left: 0;
  right: 0;
  z-index: 102;
  /* 必须比 scroll-view (101) 更高 */
  background: #FFFFFF;
  box-shadow: none;
  padding-top: 0rpx;
  padding-bottom: 0rpx;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  overflow: visible;
}

/* 💥 新增：为带筛选栏的scroll-view添加顶部内边距 */
.list-scroll-with-filter.data-v-8e954d49 {
  /* 为列表视图的筛选栏预留空间 */
  padding-top: 56rpx !important;
  /* 贴近筛选栏，去除多余空白 */
}
.calendar-scroll-with-filter.data-v-8e954d49 {
  /* 为日历视图的筛选栏预留空间 */
  padding-top: 56rpx !important;
  /* 贴近筛选栏，去除多余空白 */
}

/* 日历视图筛选栏样式 - 与列表视图保持一致 */
.calendar-filter-bar.data-v-8e954d49 {
  /* 占位按钮样式 */
}
.calendar-filter-bar .filter-placeholder.data-v-8e954d49 {
  pointer-events: none;
  /* 禁用点击 */
  opacity: 0;
  /* 隐藏但保持布局空间 */
}

/* 筛选分组 */
.filter-section.data-v-8e954d49 {
  margin-bottom: 40rpx;
}
.filter-section.data-v-8e954d49:last-child {
  margin-bottom: 0;
}

/* 分组标题 */
.section-title.data-v-8e954d49 {
  width: 104rpx;
  height: 44rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-weight: normal;
  font-size: 26rpx;
  color: #23232A;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 24rpx;
  /* Keeping this for spacing */
  display: block;
  /* Keeping this for layout */
}

/* 选项网格 */
.option-grid.data-v-8e954d49 {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  /* 进一步减少间距 */
  justify-content: flex-start;
  /* 改为左对齐，避免挤压 */
}

/* 选项项 - 根据设计规格更新 */
.option-item.data-v-8e954d49 {
  width: 162rpx;
  /* 计算后的合适宽度 */
  height: 60rpx;
  background: #F2F4FA;
  /* 未选中状态的背景色 */
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  border: 2rpx solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.option-item.active.data-v-8e954d49 {
  background: rgba(42, 97, 241, 0.2);
  /* 选中状态：蓝色背景 + 20%透明度 */
  border-color: transparent;
}
.option-item.active .option-text.data-v-8e954d49 {
  color: #023F98;
  /* 选中状态的文字颜色 */
  font-weight: normal;
}
.option-item.data-v-8e954d49:hover {
  opacity: 0.8;
}
.option-text.data-v-8e954d49 {
  width: 112rpx;
  height: 44rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  color: #66666E;
  /* 未选中状态的文字颜色 */
  line-height: 44rpx;
  text-align: center;
  /* 修改为居中对齐 */
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 筛选按钮容器 */
.filter-buttons.data-v-8e954d49 {
  display: flex;
  justify-content: flex-start;
  padding: 32rpx 0 24rpx;
  border-top: 2rpx solid #EEEEEE;
  /* 添加上边框线 */
  margin-top: 32rpx;
  /* 增加上边距 */
}

/* 筛选按钮基础样式 */
.filter-btn.data-v-8e954d49 {
  width: 340rpx;
  /* 更新宽度为340rpx */
  height: 76rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 24rpx;
  border: none;
}
.filter-btn.data-v-8e954d49:last-child {
  margin-right: 0;
}

/* 重置按钮样式 */
.reset-btn.data-v-8e954d49 {
  background: rgba(42, 97, 241, 0.1);
  /* 蓝色背景 + 10%透明度 */
}

/* 完成按钮样式 */
.complete-btn.data-v-8e954d49 {
  background: #023F98;
  /* 深蓝色背景 */
}
.complete-btn.data-v-8e954d49:hover {
  background: #1E4FD9;
}
.complete-btn.data-v-8e954d49:active {
  background: #1A43C1;
}

/* 按钮文字基础样式 */
.btn-text.data-v-8e954d49 {
  width: 56rpx;
  height: 44rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

/* 重置按钮文字样式 */
.reset-btn .btn-text.data-v-8e954d49 {
  color: #23232A;
  /* 深灰色文字 */
}

/* 完成按钮文字样式 */
.complete-btn .btn-text.data-v-8e954d49 {
  color: #FFFFFF;
  /* 白色文字 */
}

/* 活动列表滚动区域 - 白色内容容器 (根据蓝湖数据精确设置) */
.event-list-scroll.data-v-8e954d49 {
  /* 将白色背景、圆角、外边距等样式应用到这里 */
  background-color: #ffffff;
  border-top-left-radius: 32rpx;
  /* 与筛选栏圆角一致，过渡更自然 */
  border-top-right-radius: 32rpx;
  margin: 0;
  margin-top: calc(452rpx + var(--status-bar-height) - 212rpx);
  position: relative;
  z-index: 101;
  padding-top: 8rpx;
  /* 减少顶部留白 */
  flex: 1;
  box-sizing: border-box;
  padding-bottom: calc(220rpx + env(safe-area-inset-bottom));
  height: calc(100vh - 452rpx - var(--status-bar-height) + 212rpx);
}

/* 日历视图滚动区域  */
.calendar-scroll.data-v-8e954d49 {
  /* 将白色背景、圆角、外边距等样式应用到这里 */
  background-color: #ffffff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  margin: 0;
  margin-top: -158rpx;
  position: relative;
  padding-top: 24rpx;
  /* 左右内边距，让卡片和筛选栏不要贴边 */
  padding-left: 30rpx;
  padding-right: 30rpx;
  flex: 1;
  box-sizing: border-box;
  padding-bottom: 180rpx;
  /* 为底部tabBar留空 */
}
.event-card.data-v-8e954d49 {
  width: 100%;
  height: 272rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  border: 2rpx solid #EEEEEE;
  margin-bottom: 0rpx;
  padding: 24rpx;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
}
.event-card.data-v-8e954d49:last-child {
  border-bottom: none;
}
.card-left.data-v-8e954d49 {
  position: relative;
  width: 336rpx;
  height: 192rpx;
  flex-shrink: 0;
}
.event-image.data-v-8e954d49 {
  width: 100%;
  height: 100%;
  display: block;
}
.status-tag.data-v-8e954d49 {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  width: 90rpx;
  height: 40rpx;
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  /* 内部文字样式 */
  color: #23232A;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-weight: normal;
  font-size: 22rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.status-tag.ended.data-v-8e954d49 {
  background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);
}
.card-right.data-v-8e954d49 {
  flex: 1;
  padding: 16rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.event-title.data-v-8e954d49 {
  width: 346rpx;
  height: 80rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-weight: normal;
  font-size: 28rpx;
  color: #23232A;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.event-info.data-v-8e954d49 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.event-info-row.data-v-8e954d49 {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 24rpx !important;
  margin-bottom: 18rpx !important;
  flex-wrap: nowrap !important;
}
.time-location-item.data-v-8e954d49 {
  display: flex !important;
  align-items: center !important;
  gap: 8rpx !important;
  flex-shrink: 0 !important;
}
.event-info-icon.data-v-8e954d49 {
  width: 32rpx !important;
  height: 32rpx !important;
  flex-shrink: 0 !important;
}
.info-text.data-v-8e954d49 {
  width: 176rpx !important;
  height: 32rpx !important;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30" !important;
  font-weight: normal !important;
  font-size: 22rpx !important;
  color: #9B9A9A !important;
  text-align: left !important;
  font-style: normal !important;
  text-transform: none !important;
  line-height: 32rpx !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
.remaining-spots.data-v-8e954d49 {
  width: 154rpx;
  height: 40rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 1rpx solid #FB8620;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;
  /**
   * 剩余名额文字样式
   */
}
.remaining-spots .spots-count.data-v-8e954d49 {
  width: 100%;
  height: 36rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30";
  font-weight: normal;
  font-size: 20rpx;
  color: #FB8620;
  text-align: center;
  font-style: normal;
  text-transform: none;
  line-height: 36rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.empty-state.data-v-8e954d49 {
  padding: 100rpx 0;
  text-align: center;
}
.retry-container.data-v-8e954d49 {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}
.event-list-scroll.data-v-8e954d49 .up-loadmore {
  padding: 30rpx 0;
}

/* 为加载更多组件的包裹容器提供上下内边距 */
.loadmore-wrapper.data-v-8e954d49 {
  padding-top: 40rpx;
  padding-bottom: 20rpx;
}
.search-wrapper.data-v-8e954d49 .up-search {
  width: 446rpx;
  height: 40rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
}
.search-wrapper.data-v-8e954d49 .up-search .u-search__content {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);
  display: flex;
  align-items: center;
}
.search-wrapper.data-v-8e954d49 .up-search .u-search__content__icon {
  color: #c0c4cc;
}
.search-wrapper.data-v-8e954d49 .up-search .u-search__content__input {
  color: #303133;
}
@media screen and (max-width: 750rpx) {
.top-controls.data-v-8e954d49 {
    flex-direction: column;
    gap: 16rpx;
}
.top-controls .search-wrapper.data-v-8e954d49 {
    width: 446rpx;
    align-self: center;
}
}
/* up-subsection */
.data-v-8e954d49 .u-subsection {
  width: 224rpx !important;
  height: 60rpx !important;
  background: #FFFFFF !important;
  border-radius: 30rpx 30rpx 30rpx 30rpx !important;
  border: none !important;
  box-shadow: none !important;
  overflow: hidden !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  vertical-align: middle !important;
}
.data-v-8e954d49 .u-subsection__item:not(:first-child) {
  border-left: none !important;
}
.data-v-8e954d49 .u-subsection__item {
  border: none !important;
  padding: 0 !important;
  background: transparent !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 50% !important;
  position: relative !important;
  z-index: 2 !important;
}
.data-v-8e954d49 .u-subsection__item__text {
  width: 64rpx !important;
  height: 44rpx !important;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif !important;
  font-weight: normal !important;
  font-size: 32rpx !important;
  color: #23232A !important;
  text-align: center !important;
  font-style: normal !important;
  text-transform: none !important;
  white-space: nowrap !important;
  line-height: 44rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
.data-v-8e954d49 .u-subsection__item--active .u-subsection__item__text {
  color: #23232A !important;
  font-weight: normal !important;
}

/* 移动色块样式 - 确保对称居中且不被覆盖 */
.data-v-8e954d49 .u-subsection__bar {
  width: 96rpx !important;
  height: 60rpx !important;
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%) !important;
  border-radius: 30rpx !important;
  transition: all 0.3s ease !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1 !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
  transform-origin: center center !important;
}
.calendar-view.data-v-8e954d49 {
  background-color: #FFFFFF;
  padding: 0;
  padding-bottom: 0;
  box-sizing: border-box;
}

/* 搜索框样式  */
.search-wrapper.data-v-8e954d49 {
  /* 强制覆盖父容器的样式限制 */
  height: 60rpx !important;
  display: flex !important;
  align-items: center !important;
  flex-shrink: 0 !important;
}
.search-wrapper.data-v-8e954d49 .u-search {
  height: 60rpx !important;
  width: 446rpx !important;
  border: none !important;
}
.search-wrapper.data-v-8e954d49 .u-search__content {
  height: 60rpx !important;
  padding: 0 20rpx !important;
  background: #FFFFFF !important;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05) !important;
  border-radius: 30rpx !important;
}
.search-wrapper.data-v-8e954d49 .u-search__input-wrap {
  height: 60rpx !important;
  padding: 0 !important;
}
.search-wrapper.data-v-8e954d49 .u-search__input {
  height: 60rpx !important;
  line-height: 60rpx !important;
  font-size: 28rpx !important;
  color: #333333 !important;
  background-color: transparent !important;
}
.search-wrapper.data-v-8e954d49 .u-search__input::-webkit-input-placeholder {
  color: #999999 !important;
  font-size: 28rpx !important;
}
.search-wrapper.data-v-8e954d49 .u-search__input::placeholder {
  color: #999999 !important;
  font-size: 28rpx !important;
}
.search-wrapper.data-v-8e954d49 .u-search__action {
  height: 60rpx !important;
  padding: 0 8rpx !important;
}
.search-wrapper.data-v-8e954d49 .u-search__action-text {
  font-size: 28rpx !important;
  color: #333333 !important;
}