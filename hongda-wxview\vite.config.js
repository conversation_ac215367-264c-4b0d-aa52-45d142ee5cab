import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import path from 'path' // 确保引入 path

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      '@vue/runtime-dom': '@vue/runtime-core',
      '@event': path.resolve(__dirname, 'components/event')
    }
  },
  plugins: [
    uni(),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        // 全局注入uview-plus的主题文件
        // additionalData: `@import "@/uni_modules/uview-plus/theme.scss";`
      }
    }
  }
})