// 文件路径: src/main/java/com/hongda/content/mapper/HongdaCountryPolicyArticleMapper.java
package com.hongda.content.mapper;

import com.hongda.content.domain.HongdaCountryPolicyArticle;
import com.hongda.content.domain.HongdaCountryPolicyArticleTag;

import java.util.List;

/**
 * 国别政策管理Mapper接口
 * * <AUTHOR>
 * @date 2025-08-04
 */
public interface HongdaCountryPolicyArticleMapper
{
    /**
     * 查询国别政策管理
     * * @param articleId 国别政策管理主键
     * @return 国别政策管理
     */
    public HongdaCountryPolicyArticle selectHongdaCountryPolicyArticleByArticleId(Long articleId);

    /**
     * 查询国别政策管理列表
     * * @param hongdaCountryPolicyArticle 国别政策管理
     * @return 国别政策管理集合
     */
    public List<HongdaCountryPolicyArticle> selectHongdaCountryPolicyArticleList(HongdaCountryPolicyArticle hongdaCountryPolicyArticle);

    /**
     * 新增国别政策管理
     * * @param hongdaCountryPolicyArticle 国别政策管理
     * @return 结果
     */
    public int insertHongdaCountryPolicyArticle(HongdaCountryPolicyArticle hongdaCountryPolicyArticle);

    /**
     * 修改国别政策管理
     * * @param hongdaCountryPolicyArticle 国别政策管理
     * @return 结果
     */
    public int updateHongdaCountryPolicyArticle(HongdaCountryPolicyArticle hongdaCountryPolicyArticle);

    /**
     * 删除国别政策管理
     * * @param articleId 国别政策管理主键
     * @return 结果
     */
    public int deleteHongdaCountryPolicyArticleByArticleId(Long articleId);

    /**
     * 批量删除国别政策管理
     * * @param articleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaCountryPolicyArticleByArticleIds(Long[] articleIds);

    /**
     * [新增] 根据文章ID删除文章与标签的关联
     *
     * @param articleId 文章ID
     * @return 结果
     */
    public int deleteCountryPolicyArticleTagByArticleId(Long articleId);

    /**
     * [新增] 批量新增文章与标签的关联信息
     *
     * @param articleTagList 关联列表
     * @return 结果
     */
    public int batchCountryPolicyArticleTag(List<HongdaCountryPolicyArticleTag> articleTagList);
}