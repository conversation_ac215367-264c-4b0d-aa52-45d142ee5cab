"use strict";const e=require("../../common/vendor.js"),o=require("../../api/content/countryPolicy.js");if(!Array){(e.resolveComponent("u-loading-icon")+e.resolveComponent("u-icon")+e.resolveComponent("u-empty"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js"))();const t={__name:"ContentModule",props:{countryId:{type:[Number,String],required:!0},policyType:{type:String,required:!0}},setup(t){const u=t,a=e.ref([]),n=e.ref([]),l=e.ref(0),r=e.ref(!0),i=e.computed((()=>{if(r.value||0===n.value.length||!n.value[l.value])return[];const e=n.value[l.value].name;return"全部"===e?a.value:a.value.filter((o=>o.categoryName===e))})),c=async()=>{if(r.value=!0,!u.countryId||!u.policyType)return a.value=[],n.value=[],void(r.value=!1);try{const e=await o.listCountryPolicyArticle({countryId:u.countryId,policyType:u.policyType,pageNum:1,pageSize:100});a.value=e.rows||[];const t=[...new Set(a.value.map((e=>e.categoryName)).filter(Boolean))];n.value=[{name:"全部"},...t.map((e=>({name:e})))],l.value=0}catch(e){console.error(`获取${u.policyType}政策文章失败:`,e),a.value=[],n.value=[]}finally{r.value=!1}};return e.watch((()=>u.policyType),((e,o)=>{e&&e!==o&&c()})),e.onMounted((()=>{c()})),(o,t)=>e.e({a:e.f(n.value,((o,t,u)=>({a:e.t(o.name),b:t,c:l.value===t?1:"",d:e.o((e=>(e=>{l.value=e})(t)),t)}))),b:r.value},r.value?{c:e.p({mode:"circle",text:"加载中...",size:"24"})}:i.value.length>0?{e:e.f(i.value,((o,t,u)=>({a:e.t(o.title),b:"4629d60d-1-"+u,c:o.articleId,d:e.o((t=>{return u=o.articleId,void e.index.navigateTo({url:`/pages_sub/pages_country/policy_detail?id=${u}`});var u}),o.articleId)}))),f:e.p({name:"arrow-right",color:"#BFBFBF",size:"16"})}:{g:e.p({mode:"list",text:"暂无相关内容"})},{d:i.value.length>0})}},u=e._export_sfc(t,[["__scopeId","data-v-4629d60d"]]);wx.createComponent(u);
