"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/tools.js"),n=require("../../utils/date.js"),r=require("../../utils/image.js"),a=require("../../utils/location.js"),s={__name:"EventCard",props:{event:{type:Object,required:!0}},setup(s){const i=e.ref(""),u=e.ref("");e.onMounted((()=>{const t=e.index.getStorageSync("staticAssets");i.value=(null==t?void 0:t.list_time)||"",u.value=(null==t?void 0:t.list_location)||""}));const o=e=>{switch(e){case 0:return"即将开始";case 1:return"报名中";case 2:return"报名截止";default:return"未知"}},c=e=>{switch(e){case 0:return"not-started";case 1:return"open";case 2:return"ended";default:return"unknown"}};return(l,v)=>e.e({a:e.unref(r.getFullImageUrl)(s.event.coverImageUrl),b:void 0!==s.event.registrationStatus},void 0!==s.event.registrationStatus?{c:e.t(o(s.event.registrationStatus)),d:e.n(c(s.event.registrationStatus))}:{},{e:e.t(s.event.title),f:i.value,g:e.t(e.unref(n.formatEventDate)(s.event.startTime)),h:u.value,i:e.t(e.unref(a.formatEventLocation)(s.event)),j:e.t(e.unref(t.calculateRemainingSpots)(s.event.maxParticipants,s.event.registeredCount)),k:e.o((e=>l.$emit("click",s.event)))})}},i=e._export_sfc(s,[["__scopeId","data-v-f8aba058"]]);wx.createComponent(i);
