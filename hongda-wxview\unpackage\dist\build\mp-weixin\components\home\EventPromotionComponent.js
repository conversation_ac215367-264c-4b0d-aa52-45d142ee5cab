"use strict";const e=require("../../common/vendor.js"),o=require("../../api/data/event.js"),t=require("../../utils/image.js");if(!Array){(e.resolveComponent("u-skeleton")+e.resolveComponent("up-button"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-skeleton/u-skeleton.js")+(()=>"../../uni_modules/uview-plus/components/u-button/u-button.js"))();const i={__name:"EventPromotionComponent",setup(i){const n=e.ref(""),l=e.ref(""),r=e.ref([]),a=e.ref(!0),s=e.ref(0),u=e=>{s.value=e.detail.current},c=o=>{o&&o.id?e.index.navigateTo({url:`/pages_sub/pages_event/detail?id=${o.id}`}):console.warn("推广卡片数据异常")},d=e=>{console.log("图片加载成功")},p=e=>{console.error("图片加载失败:",e)};return e.onMounted((()=>{try{const o=e.index.getStorageSync("staticAssets");n.value=(null==o?void 0:o["flame-icon"])||"",l.value=(null==o?void 0:o["thumb-up-icon"])||""}catch(i){}(async()=>{try{const e=await o.getPromotionEventListApi({pageSize:10});if(e&&200===e.code){let o=null;e.rows&&Array.isArray(e.rows)?o=e.rows:e.data&&e.data.rows&&Array.isArray(e.data.rows)&&(o=e.data.rows),o&&Array.isArray(o)&&o.length>0?(r.value=o.map((e=>({id:e.id,title:e.promotionTitle||e.title,image:t.getFullImageUrl(e.promotionImageUrl||e.coverImageUrl),iconUrl:e.iconUrl,linkUrl:`/pages_sub/pages_event/detail?id=${e.id}`,descriptionLine1:e.summary||"官方认证，品质保证",descriptionLine2:e.sellPoint||"干货满满，不容错过"}))),console.log("获取推广活动成功，数量:",r.value.length)):(r.value=[],console.warn("暂无推广活动数据"))}else r.value=[],console.warn("获取推广活动失败，响应码:",null==e?void 0:e.code)}catch(e){console.error("获取推广数据失败:",e.message),r.value=[]}finally{a.value=!1}})()})),(o,i)=>e.e({a:a.value},a.value?{b:e.p({loading:!0,animate:!0,rows:4,title:!0,titleWidth:"60%",rowsWidth:"['100%', '40%', '40%', '100%']",rowsHeight:"['180px', '20px', '20px', '40px']"})}:r.value.length>0?e.e({d:e.f(r.value,((o,i,r)=>e.e({a:o.iconUrl},o.iconUrl?{b:e.unref(t.getFullImageUrl)(o.iconUrl)}:{},{c:e.t(o.title),d:o.image,e:e.o(p,o.id||i),f:e.o(d,o.id||i),g:o.descriptionLine1},o.descriptionLine1?{h:n.value,i:e.t(o.descriptionLine1)}:{},{j:o.descriptionLine2},o.descriptionLine2?{k:l.value,l:e.t(o.descriptionLine2)}:{},{m:e.o((e=>c(o)),o.id||i),n:"a45ee1b8-1-"+r,o:e.o((e=>c(o)),o.id||i),p:o.id||i}))),e:e.p({type:"primary",shape:"square",text:"立即报名",size:"large",customStyle:{backgroundColor:"#023F98",height:"68rpx",width:"654rpx",borderRadius:"8rpx",margin:"0 auto"}}),f:s.value,g:e.o(u),h:r.value.length>1},r.value.length>1?{i:e.f(r.value,((o,t,i)=>({a:t,b:s.value===t?1:"",c:e.o((e=>(e=>{s.value=e,console.log("点击指示器切换到索引:",e)})(t)),t)})))}:{}):{},{c:r.value.length>0})}},n=e._export_sfc(i,[["__scopeId","data-v-a45ee1b8"]]);wx.createComponent(n);
