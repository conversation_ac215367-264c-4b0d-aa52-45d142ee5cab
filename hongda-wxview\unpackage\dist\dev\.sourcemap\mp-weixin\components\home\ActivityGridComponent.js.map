{"version": 3, "file": "ActivityGridComponent.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9BY3Rpdml0eUdyaWRDb21wb25lbnQudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"activity-section section-wrapper\">\r\n        <view class=\"section-header\">\r\n            <view class=\"title-wrapper\">\r\n                <text class=\"title-main\">{{ titleParts.main }}</text>\r\n                <text class=\"title-gradient\">{{ titleParts.gradient }}</text>\r\n            </view>\r\n\t\t\t<view class=\"section-more\" @click=\"goToEventList\">\r\n\t\t\t\t<text>更多</text>\r\n\t\t\t\t<up-icon name=\"arrow-right\" size=\"14\"></up-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view v-if=\"isLoading\" class=\"loading-state\">\r\n\t\t\t<up-loading-icon mode=\"spinner\" size=\"40\"></up-loading-icon>\r\n\t\t\t<text class=\"loading-text\">加载中...</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view v-else class=\"activity-grid\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"activity-card\" \r\n\t\t\t\tv-for=\"(item, index) in activityList\" \r\n\t\t\t\t:key=\"item.id || index\"\r\n\t\t\t\t@click=\"goToEventDetail(item)\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"image-wrapper\">\r\n\t\t\t\t\t<image \r\n\t\t\t\t\t\tclass=\"activity-image\" \r\n\t\t\t\t\t\t:src=\"item.image\" \r\n\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t:lazy-load=\"true\"\r\n\t\t\t\t\t></image>\r\n                    <view class=\"status-tag\" :class=\"item.statusClass\">{{ item.status }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"activity-info\">\r\n\t\t\t\t\t<text class=\"activity-title\">{{ item.title }}</text>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 时间和地点信息行 -->\r\n\t\t\t\t\t<view class=\"activity-meta-row\">\r\n                        <view class=\"time-info\">\r\n                            <image class=\"meta-icon\" :src=\"icons.time\" mode=\"widthFix\" />\r\n                            <text class=\"time-text\">{{ item.dateTime }}</text>\r\n                            <text class=\"weekday-text\">{{ item.weekday }}</text>\r\n                        </view>\r\n                        <view class=\"location-info\">\r\n                            <image class=\"meta-icon\" :src=\"icons.location\" mode=\"widthFix\" />\r\n                            <text class=\"location-text\">{{ item.location }}</text>\r\n                        </view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n                    <!-- 剩余名额标签 -->\r\n\t\t\t\t\t<view class=\"remaining-spots-tag\">\r\n\t\t\t\t\t\t<text class=\"spots-label\">剩余名额：</text>\r\n\t\t\t\t\t\t<text class=\"spots-count\">{{ item.remainingSpots }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 空状态 -->\r\n\t\t<view v-if=\"!isLoading && activityList.length === 0\" class=\"empty-state\">\r\n\t\t\t<up-empty mode=\"data\" text=\"暂无精选活动\" textColor=\"#909399\" iconSize=\"80\"></up-empty>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed, watch } from 'vue';\r\nimport { getHotEventListApi } from '@/api/data/event.js';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\nimport { formatEventStatus, getStatusClass, calculateRemainingSpots } from '@/utils/tools.js';\r\nimport { formatActivityLocation } from '@/utils/location.js';\r\n\r\n// 事件定义：向父级报告是否全部加载完成\r\nconst emit = defineEmits(['all-loaded-change'])\r\n\r\n// 配置：首屏显示与每次加载数量\r\nconst INITIAL_COUNT = 4\r\nconst STEP_COUNT = 2\r\nconst MAX_FETCH_COUNT = 100\r\n\r\n// 图标资源\r\nconst icons = {\r\n  time: '',\r\n  location: ''\r\n};\r\n\r\n// 加载状态与列表数据\r\nconst isLoading = ref(false)\r\nconst isLoadingMore = ref(false)\r\nconst allEvents = ref([])\r\nconst visibleCount = ref(0)\r\n\r\n// 计算属性：标题拆分\r\nconst titleParts = computed(() => {\r\n  const title = '精选活动';\r\n  return { main: title.slice(0, 2), gradient: title.slice(2) };\r\n});\r\n\r\n// 计算属性：当前展示的活动列表\r\nconst activityList = computed(() => {\r\n  return allEvents.value.slice(0, Math.min(visibleCount.value, allEvents.value.length))\r\n})\r\n\r\n// 计算属性：是否还有更多\r\nconst hasMore = computed(() => visibleCount.value < allEvents.value.length)\r\n\r\n// 监听 hasMore 变化，通知父组件\r\nwatch(hasMore, (newVal) => {\r\n  emit('all-loaded-change', !newVal)\r\n})\r\n\r\n// --- 生命周期 ---\r\nonMounted(() => {\r\n  try {\r\n    const assets = uni.getStorageSync('staticAssets')\r\n    icons.time = assets?.detail_icon_time || ''\r\n    icons.location = assets?.detail_icon_location || ''\r\n  } catch (e) {}\r\n  fetchHotEvents();\r\n});\r\n\r\n// --- 工具函数：安全解析日期 ---\r\n/**\r\n * 安全解析多种日期输入为 Date 对象\r\n * @param {string|number|Date} input 原始日期输入\r\n * @returns {Date|null}\r\n */\r\nconst parseSafeDate = (input) => {\r\n  if (!input) return null;\r\n  if (input instanceof Date) {\r\n    return isNaN(input.getTime()) ? null : input;\r\n  }\r\n  if (typeof input === 'number') {\r\n    const d = new Date(input);\r\n    return isNaN(d.getTime()) ? null : d;\r\n  }\r\n  if (typeof input === 'string') {\r\n    let s = input.trim();\r\n    if (/^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$/.test(s)) {\r\n      s = s.replace(' ', 'T');\r\n    }\r\n    let d = new Date(s);\r\n    if (isNaN(d.getTime())) {\r\n      const m = s.match(/^(\\d{4})-(\\d{1,2})-(\\d{1,2})(?:[ T](\\d{1,2}):(\\d{2})(?::(\\d{2}))?)?/);\r\n      if (m) {\r\n        const y = m[1];\r\n        const mo = m[2];\r\n        const day = m[3];\r\n        const rest = m[4] ? ` ${m[4]}:${m[5]}:${m[6] || '00'}` : '';\r\n        d = new Date(`${y}/${mo}/${day}${rest}`);\r\n      }\r\n    }\r\n    return isNaN(d.getTime()) ? null : d;\r\n  }\r\n  return null;\r\n};\r\n\r\n/**\r\n * 格式化活动日期为 M.DD\r\n * @param {string|number|Date} dateString 日期输入\r\n * @returns {string}\r\n */\r\nconst formatActivityDate = (dateString) => {\r\n  if (!dateString) return '';\r\n  const date = parseSafeDate(dateString);\r\n  if (!date) return '';\r\n  const month = date.getMonth() + 1;\r\n  const day = String(date.getDate()).padStart(2, '0');\r\n  return `${month}.${day}`;\r\n};\r\n\r\n/**\r\n * 获取星期文本\r\n * @param {string|number|Date} dateString 日期输入\r\n * @returns {string}\r\n */\r\nconst getWeekday = (dateString) => {\r\n  if (!dateString) return '';\r\n  const date = parseSafeDate(dateString);\r\n  if (!date) return '';\r\n  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n  return weekdays[date.getDay()];\r\n};\r\n\r\n/**\r\n * 计算剩余名额\r\n * @param {number} maxParticipants 最大人数\r\n * @param {number} registeredCount 已报名人数\r\n * @returns {number|string}\r\n */\r\nconst calculateRemaining = (maxParticipants, registeredCount) => {\r\n  if (!maxParticipants || maxParticipants <= 0) {\r\n    return '不限';\r\n  }\r\n  const remaining = maxParticipants - (registeredCount || 0);\r\n  return remaining > 0 ? remaining : 0;\r\n};\r\n\r\n// formatActivityLocation 函数已从 @/utils/location.js 导入\r\n\r\n/**\r\n * 拉取热门活动并初始化可见数量\r\n * 一次性拉取较多数据到 allEvents，首屏显示 4 条\r\n */\r\nconst fetchHotEvents = async () => {\r\n  if (isLoading.value) return;\r\n  isLoading.value = true;\r\n  try {\r\n    const response = await getHotEventListApi(MAX_FETCH_COUNT);\r\n    const events = response.data || response.rows || [];\r\n    const registeringRows = events.filter(item => item.registrationStatus === 1);\r\n\r\n    allEvents.value = registeringRows.map(item => ({\r\n      id: item.id,\r\n      title: item.title,\r\n      image: getFullImageUrl(item.coverImageUrl),\r\n      location: formatActivityLocation(item),\r\n      status: item.registrationStatusText || '报名中',\r\n      statusClass: 'status-registering',\r\n      dateTime: formatActivityDate(item.startTime),\r\n      weekday: getWeekday(item.startTime),\r\n      remainingSpots: calculateRemaining(item.maxParticipants, item.registeredCount)\r\n    }));\r\n\r\n    // 初始化可视数量\r\n    visibleCount.value = Math.min(INITIAL_COUNT, allEvents.value.length)\r\n\r\n    // 首次拉取后即告知父级是否全部加载完\r\n    emit('all-loaded-change', !(visibleCount.value < allEvents.value.length))\r\n  } catch (error) {\r\n    console.error('获取热门活动失败:', error);\r\n    // 失败兜底示例数据\r\n    allEvents.value = [\r\n      { \r\n        id: 1, \r\n        title: '2025 Ozen卖家增长峰会·北京站', \r\n        status: '报名中', \r\n        statusClass: 'status-registering', \r\n        location: '北京', \r\n        image: 'https://via.placeholder.com/170x100/3c9cff/fff?text=活动1',\r\n        dateTime: '7.15',\r\n        weekday: '周二',\r\n        remainingSpots: '29'\r\n      },\r\n    ];\r\n    visibleCount.value = Math.min(INITIAL_COUNT, allEvents.value.length)\r\n    emit('all-loaded-change', !(visibleCount.value < allEvents.value.length))\r\n    uni.showToast({\r\n      title: '获取活动数据失败，显示示例数据',\r\n      icon: 'none',\r\n      duration: 2000\r\n    });\r\n  } finally {\r\n    isLoading.value = false;\r\n  }\r\n};\r\n\r\n/**\r\n * 触发加载更多（每次 +2），由父级 scrolltolower 调用\r\n */\r\nconst loadMore = () => {\r\n  if (isLoadingMore.value) return\r\n  if (!hasMore.value) return\r\n  isLoadingMore.value = true\r\n  // 小延迟以防止连续触发\r\n  setTimeout(() => {\r\n    visibleCount.value = Math.min(visibleCount.value + STEP_COUNT, allEvents.value.length)\r\n    isLoadingMore.value = false\r\n  }, 50)\r\n}\r\n\r\n/**\r\n * 跳转活动详情\r\n * @param {Object} activity 活动数据\r\n */\r\nconst goToEventDetail = (activity) => {\r\n  uni.navigateTo({\r\n    url: `/pages_sub/pages_event/detail?id=${activity.id}`\r\n  });\r\n};\r\n\r\n/**\r\n * 跳转活动列表页\r\n */\r\nconst goToEventList = () => {\r\n  uni.switchTab({\r\n    url: '/pages/event/index',\r\n    fail: () => {\r\n      uni.navigateTo({ url: '/pages/event/index' });\r\n    }\r\n  });\r\n};\r\n\r\n// 对外暴露 loadMore，供父组件调用\r\ndefineExpose({ loadMore })\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.section-wrapper {\r\n\tmargin: 24rpx;\r\n\tpadding: 20rpx;\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 16rpx;\r\n}\r\n.section-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding-bottom: 20rpx;\r\n}\r\n.title-wrapper {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n.title-main {\r\n    /* “精选”的样式 */\r\n    font-size: 40rpx;\r\n    font-weight: 400;\r\n    color: #023F98;\r\n    font-family: 'YouSheBiaoTiHei', 'Alibaba PuHuiTi 3.0', sans-serif;\r\n}\r\n.title-gradient {\r\n    /* “活动”的样式 */\r\n    font-size: 40rpx;\r\n    font-weight: 400;\r\n    background-image: linear-gradient(91.61deg, #FFAD22 0%, #FFBB87 100%);\r\n    -webkit-background-clip: text;\r\n    background-clip: text;\r\n    color: transparent;\r\n    font-family: 'YouSheBiaoTiHei', 'Alibaba PuHuiTi 3.0', sans-serif;\r\n}\r\n.section-more {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #909399;\r\n\tcursor: pointer;\r\n\ttransition: color 0.2s ease;\r\n\t\r\n\t&:active {\r\n\t\tcolor: #f56c6c;\r\n\t}\r\n}\r\n\r\n// 加载状态样式\r\n.loading-state {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 60rpx 0;\r\n\t\r\n\t.loading-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #909399;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n}\r\n\r\n// 空状态样式\r\n.empty-state {\r\n\tpadding: 60rpx 0;\r\n\ttext-align: center;\r\n}\r\n\r\n.activity-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tjustify-content: space-between;\r\n}\r\n.activity-card {\r\n\twidth: calc(50% - 10rpx);\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 16rpx;\r\n\tmargin-bottom: 20rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: none;\r\n\ttransition: transform 0.2s ease, box-shadow 0.2s ease;\r\n\r\n\t// 点击效果\r\n\t&:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.image-wrapper {\r\n\t\tposition: relative;\r\n\t\twidth: 336rpx;\r\n\t\theight: 192rpx; \r\n\t\tmargin: 0 auto; \r\n\t\tborder-radius: 16rpx; \r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.activity-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 16rpx;\r\n\t}\r\n\r\n\t.activity-info {\r\n\t\tpadding: 16rpx;\r\n\t}\r\n\r\n\t.activity-title {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\r\n\t\tfont-weight: normal;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #23232A;\r\n\t\ttext-align: justify;\r\n\t\tfont-style: normal;\r\n\t\ttext-transform: none;\r\n\t\tline-height: 40rpx; \r\n\t\tmargin-bottom: 12rpx;\r\n\t\tword-break: break-word;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\tline-clamp: 2; \r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t}\r\n\r\n    // 时间和地点信息行\r\n    .activity-meta-row {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-start;\r\n        gap: 46rpx; \r\n        margin-bottom: 12rpx;\r\n\t\t\r\n        .time-info {\r\n            display: flex;\r\n            align-items: center;\r\n            flex: none;\r\n\t\t\t\r\n\t\t\t.meta-icon {\r\n\t\t\t\twidth: 24rpx;\r\n\t\t\t\theight: 24rpx;\r\n\t\t\t}\r\n\t\t\t\r\n            .time-text {\r\n\t\t\t\twidth: 66rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\r\n\t\t\t\tfont-weight: normal;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #9B9A9A;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tfont-style: normal;\r\n\t\t\t\ttext-transform: none;\r\n\t\t\t\tline-height: 32rpx;\r\n                margin-left: 6rpx;\r\n                margin-right: 6rpx; \r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.weekday-text {\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #9B9A9A;\r\n\t\t\t\tline-height: 32rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n        .location-info {\r\n            display: flex;\r\n            align-items: center;\r\n            flex: none; \r\n\t\t\t\r\n\t\t\t.meta-icon {\r\n\t\t\t\twidth: 24rpx;\r\n\t\t\t\theight: 24rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.location-text {\r\n\t\t\t\twidth: 66rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\r\n\t\t\t\tfont-weight: normal;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #9B9A9A;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tfont-style: normal;\r\n\t\t\t\ttext-transform: none;\r\n\t\t\t\tline-height: 32rpx;\r\n\t\t\t\tmargin-left: 8rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n    // 剩余名额\r\n\t.remaining-spots-tag {\r\n        display: inline-flex;\r\n        align-items: center;\r\n        justify-content: flex-start;\r\n        height: 40rpx;\r\n        border-radius: 4rpx;\r\n        border: 1rpx solid #FB8620;\r\n        background-color: transparent;\r\n        padding: 0 8rpx 0 4rpx; \r\n        box-sizing: border-box;\r\n        align-self: flex-start;\r\n        white-space: nowrap; \r\n\r\n        .spots-label {\r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\r\n\t\t\t\tfont-weight: normal;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #FB8620;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tfont-style: normal;\r\n\t\t\t\ttext-transform: none;\r\n\t\t\t\tline-height: 36rpx;\r\n                white-space: nowrap;\r\n\t\t\t}\r\n\r\n\t\t\t.spots-count {\r\n                width: auto; \r\n                text-align: right; \r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\r\n\t\t\t\tfont-weight: normal;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #FB8620;\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t\twhite-space: nowrap;\r\n                font-variant-numeric: tabular-nums;\r\n\t\t\t}\r\n\t}\r\n\r\n    .status-tag {\r\n        position: absolute;\r\n        top: 12rpx;\r\n        left: 12rpx;\r\n        width: 90rpx;\r\n        height: 40rpx;\r\n        background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n        border-radius: 20rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        box-sizing: border-box;\r\n        color: #23232A;\r\n        font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n        font-weight: normal;\r\n        font-size: 22rpx;\r\n        text-align: left;\r\n        font-style: normal;\r\n        text-transform: none;\r\n\r\n        &.ended {\r\n            background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);\r\n            color: #FFFFFF;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "computed", "watch", "onMounted", "uni", "getHotEventListApi", "getFullImageUrl", "formatActivityLocation"], "mappings": ";;;;;;;;;;;;;;;;;;AA6EA,MAAM,gBAAgB;AACtB,MAAM,aAAa;AACnB,MAAM,kBAAkB;;;;;AALxB,UAAM,OAAO;AAQb,UAAM,QAAQ;AAAA,MACZ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAGA,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,gBAAgBA,cAAG,IAAC,KAAK;AAC/B,UAAM,YAAYA,cAAG,IAAC,EAAE;AACxB,UAAM,eAAeA,cAAG,IAAC,CAAC;AAG1B,UAAM,aAAaC,cAAQ,SAAC,MAAM;AAChC,YAAM,QAAQ;AACd,aAAO,EAAE,MAAM,MAAM,MAAM,GAAG,CAAC,GAAG,UAAU,MAAM,MAAM,CAAC,EAAC;AAAA,IAC5D,CAAC;AAGD,UAAM,eAAeA,cAAQ,SAAC,MAAM;AAClC,aAAO,UAAU,MAAM,MAAM,GAAG,KAAK,IAAI,aAAa,OAAO,UAAU,MAAM,MAAM,CAAC;AAAA,IACtF,CAAC;AAGD,UAAM,UAAUA,cAAQ,SAAC,MAAM,aAAa,QAAQ,UAAU,MAAM,MAAM;AAG1EC,kBAAAA,MAAM,SAAS,CAAC,WAAW;AACzB,WAAK,qBAAqB,CAAC,MAAM;AAAA,IACnC,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACd,UAAI;AACF,cAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAChD,cAAM,QAAO,iCAAQ,qBAAoB;AACzC,cAAM,YAAW,iCAAQ,yBAAwB;AAAA,MACrD,SAAW,GAAG;AAAA,MAAE;AACd;IACF,CAAC;AAQD,UAAM,gBAAgB,CAAC,UAAU;AAC/B,UAAI,CAAC;AAAO,eAAO;AACnB,UAAI,iBAAiB,MAAM;AACzB,eAAO,MAAM,MAAM,QAAS,CAAA,IAAI,OAAO;AAAA,MACxC;AACD,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,IAAI,KAAK,KAAK;AACxB,eAAO,MAAM,EAAE,QAAS,CAAA,IAAI,OAAO;AAAA,MACpC;AACD,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,IAAI,MAAM;AACd,YAAI,wCAAwC,KAAK,CAAC,GAAG;AACnD,cAAI,EAAE,QAAQ,KAAK,GAAG;AAAA,QACvB;AACD,YAAI,IAAI,IAAI,KAAK,CAAC;AAClB,YAAI,MAAM,EAAE,QAAO,CAAE,GAAG;AACtB,gBAAM,IAAI,EAAE,MAAM,qEAAqE;AACvF,cAAI,GAAG;AACL,kBAAM,IAAI,EAAE,CAAC;AACb,kBAAM,KAAK,EAAE,CAAC;AACd,kBAAM,MAAM,EAAE,CAAC;AACf,kBAAM,OAAO,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,KAAK;AACzD,gBAAI,oBAAI,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,UACxC;AAAA,QACF;AACD,eAAO,MAAM,EAAE,QAAS,CAAA,IAAI,OAAO;AAAA,MACpC;AACD,aAAO;AAAA,IACT;AAOA,UAAM,qBAAqB,CAAC,eAAe;AACzC,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,cAAc,UAAU;AACrC,UAAI,CAAC;AAAM,eAAO;AAClB,YAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,KAAK,IAAI,GAAG;AAAA,IACxB;AAOA,UAAM,aAAa,CAAC,eAAe;AACjC,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,cAAc,UAAU;AACrC,UAAI,CAAC;AAAM,eAAO;AAClB,YAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,aAAO,SAAS,KAAK,OAAM,CAAE;AAAA,IAC/B;AAQA,UAAM,qBAAqB,CAAC,iBAAiB,oBAAoB;AAC/D,UAAI,CAAC,mBAAmB,mBAAmB,GAAG;AAC5C,eAAO;AAAA,MACR;AACD,YAAM,YAAY,mBAAmB,mBAAmB;AACxD,aAAO,YAAY,IAAI,YAAY;AAAA,IACrC;AAQA,UAAM,iBAAiB,YAAY;AACjC,UAAI,UAAU;AAAO;AACrB,gBAAU,QAAQ;AAClB,UAAI;AACF,cAAM,WAAW,MAAMC,kCAAmB,eAAe;AACzD,cAAM,SAAS,SAAS,QAAQ,SAAS,QAAQ,CAAA;AACjD,cAAM,kBAAkB,OAAO,OAAO,UAAQ,KAAK,uBAAuB,CAAC;AAE3E,kBAAU,QAAQ,gBAAgB,IAAI,WAAS;AAAA,UAC7C,IAAI,KAAK;AAAA,UACT,OAAO,KAAK;AAAA,UACZ,OAAOC,YAAAA,gBAAgB,KAAK,aAAa;AAAA,UACzC,UAAUC,eAAsB,uBAAC,IAAI;AAAA,UACrC,QAAQ,KAAK,0BAA0B;AAAA,UACvC,aAAa;AAAA,UACb,UAAU,mBAAmB,KAAK,SAAS;AAAA,UAC3C,SAAS,WAAW,KAAK,SAAS;AAAA,UAClC,gBAAgB,mBAAmB,KAAK,iBAAiB,KAAK,eAAe;AAAA,QAC9E,EAAC;AAGF,qBAAa,QAAQ,KAAK,IAAI,eAAe,UAAU,MAAM,MAAM;AAGnE,aAAK,qBAAqB,EAAE,aAAa,QAAQ,UAAU,MAAM,OAAO;AAAA,MACzE,SAAQ,OAAO;AACdH,sBAAc,MAAA,MAAA,SAAA,oDAAA,aAAa,KAAK;AAEhC,kBAAU,QAAQ;AAAA,UAChB;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,UAAU;AAAA,YACV,OAAO;AAAA,YACP,UAAU;AAAA,YACV,SAAS;AAAA,YACT,gBAAgB;AAAA,UACjB;AAAA,QACP;AACI,qBAAa,QAAQ,KAAK,IAAI,eAAe,UAAU,MAAM,MAAM;AACnE,aAAK,qBAAqB,EAAE,aAAa,QAAQ,UAAU,MAAM,OAAO;AACxEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACL,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAKA,UAAM,WAAW,MAAM;AACrB,UAAI,cAAc;AAAO;AACzB,UAAI,CAAC,QAAQ;AAAO;AACpB,oBAAc,QAAQ;AAEtB,iBAAW,MAAM;AACf,qBAAa,QAAQ,KAAK,IAAI,aAAa,QAAQ,YAAY,UAAU,MAAM,MAAM;AACrF,sBAAc,QAAQ;AAAA,MACvB,GAAE,EAAE;AAAA,IACP;AAMA,UAAM,kBAAkB,CAAC,aAAa;AACpCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oCAAoC,SAAS,EAAE;AAAA,MACxD,CAAG;AAAA,IACH;AAKA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,QACL,MAAM,MAAM;AACVA,wBAAAA,MAAI,WAAW,EAAE,KAAK,qBAAsB,CAAA;AAAA,QAC7C;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAa,EAAE,SAAQ,CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtSzB,GAAG,gBAAgB,SAAS;"}