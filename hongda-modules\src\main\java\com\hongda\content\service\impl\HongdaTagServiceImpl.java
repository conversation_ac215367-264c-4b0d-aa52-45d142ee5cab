package com.hongda.content.service.impl;

import com.hongda.common.exception.ServiceException;
import com.hongda.content.domain.HongdaTag;
import com.hongda.content.mapper.ArticleTagRelationMapper;
import com.hongda.content.mapper.HongdaTagMapper;
import com.hongda.content.service.IHongdaTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 资讯分类标签Service业务层处理
 * (健壮的最终版 - 统一删除入口)
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class HongdaTagServiceImpl implements IHongdaTagService
{
    @Autowired
    private HongdaTagMapper hongdaTagMapper;

    @Autowired
    private ArticleTagRelationMapper articleTagRelationMapper;

    @Override
    public HongdaTag selectHongdaTagById(Long id)
    {
        return hongdaTagMapper.selectHongdaTagById(id);
    }

    @Override
    public List<HongdaTag> selectHongdaTagList(HongdaTag hongdaTag)
    {
        return hongdaTagMapper.selectHongdaTagList(hongdaTag);
    }

    @Override
    public int insertHongdaTag(HongdaTag hongdaTag)
    {
        return hongdaTagMapper.insertHongdaTag(hongdaTag);
    }

    @Override
    public int updateHongdaTag(HongdaTag hongdaTag)
    {
        return hongdaTagMapper.updateHongdaTag(hongdaTag);
    }

    /**
     * [修改] 统一的删除入口，智能判断单/多删除
     * 这个方法会由 Controller 的 @DeleteMapping("/{ids}") 调用
     */
    @Override
    @Transactional
    public int deleteHongdaTagByIds(Long[] ids)
    {
        if (ids == null || ids.length == 0) {
            return 0;
        }

        // 逻辑判断：如果传入的数组中只有一个ID，则按“单个删除”的逻辑处理
        if (ids.length == 1)
        {
            Long tagId = ids[0];
            int count = articleTagRelationMapper.countArticlesByTagId(tagId);
            if (count > 0)
            {
                HongdaTag tag = hongdaTagMapper.selectHongdaTagById(tagId);
                String tagName = (tag != null) ? tag.getName() : "";
                // 抛出带有具体数量和名称的异常信息
                throw new ServiceException(String.format("标签 [%s] 已被 %d 篇资讯关联，无法删除", tagName, count));
            }
        }
        // 否则，按“批量删除”的逻辑处理
        else
        {
            Long usageCount = articleTagRelationMapper.selectUsageCountByTagIds(ids);
            if (usageCount > 0)
            {
                // 抛出通用的提示信息
                throw new ServiceException("删除失败，所选标签中存在正在被文章使用的标签，请先解除关联！");
            }
        }

        // 所有检查通过后，才执行真正的删除数据库操作
        return hongdaTagMapper.deleteHongdaTagByIds(ids);
    }

    /**
     * 删除单个资讯分类标签信息
     * (这个方法在当前的 Controller 设计下不会被直接调用，但为了接口的完整性而保留)
     * (即便被调用，它也会走上面的统一删除入口，保证逻辑正确)
     */
    @Override
    @Transactional
    public int deleteHongdaTagById(Long id)
    {
        return this.deleteHongdaTagByIds(new Long[]{id});
    }
}