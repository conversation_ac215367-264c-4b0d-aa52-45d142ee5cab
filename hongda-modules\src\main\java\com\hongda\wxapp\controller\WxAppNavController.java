package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.platform.domain.HongdaNav;
import com.hongda.platform.service.IHongdaNavService;
import com.hongda.wxapp.domain.vo.NavVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import com.hongda.common.annotation.Anonymous;

@Anonymous
@RestController
@RequestMapping("/api/v1/nav")
public class WxAppNavController extends BaseController {

    @Autowired
    private IHongdaNavService hongdaNavService;

    // Tab Bar 页面列表，用于判断跳转方式
    private static final List<String> TAB_BAR_PAGES = Arrays.asList(
            "/pages/index/index",
            "/pages/article/index",
            "/pages/event/index",
            "/pages/country/index",
            "/pages/profile/index"
    );

    @GetMapping("/list")
    public AjaxResult getNavListByPosition(@RequestParam("positionCode") String positionCode) {
        HongdaNav queryParams = new HongdaNav();
        queryParams.setPositionCode(positionCode);
        queryParams.setStatus(1); // 只查询已启用的导航项

        // 1. 从服务层获取数据
        List<HongdaNav> navList = hongdaNavService.selectHongdaNavList(queryParams);

        // 2. [核心修改] 使用新的、统一的逻辑将数据转换为VO列表
        List<NavVO> voList = navList.stream().map(nav -> {
            NavVO vo = new NavVO();
            BeanUtils.copyProperties(nav, vo); // 复制ID、标题、图标等基本属性

            String linkType = nav.getLinkType();
            String linkTarget = nav.getLinkTarget();

            if (linkType == null || linkTarget == null) {
                // 如果没有配置链接，直接返回
                return vo;
            }

            // 使用与广告控制器完全相同的逻辑来确保全站跳转行为一致
            switch (linkType) {
                case "ARTICLE":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_article/detail?id=" + linkTarget);
                    break;
                case "EVENT":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_event/detail?id=" + linkTarget);
                    break;
                case "COUNTRY":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_country/detail?id=" + linkTarget);
                    break;
                case "PARK":
                    vo.setLinkType("INTERNAL_PAGE");
                    vo.setLinkTarget("/pages_sub/pages_other/park_detail?id=" + linkTarget);
                    break;
                case "PAGE":
                    // 如果是内部页面，需要判断是普通页还是Tab页
                    if (TAB_BAR_PAGES.contains(linkTarget.split("\\?")[0])) {
                        vo.setLinkType("TAB_PAGE");
                    } else {
                        vo.setLinkType("INTERNAL_PAGE");
                    }
                    vo.setLinkTarget(linkTarget);
                    break;
                case "EXTERNAL":
                    vo.setLinkType("EXTERNAL_LINK");
                    vo.setLinkTarget(linkTarget); // 外部链接直接使用
                    break;
                case "NONE":
                    // 无链接，不设置 linkType 和 linkTarget
                    break;
            }
            return vo;
        }).collect(Collectors.toList());

        return AjaxResult.success(voList);
    }
}