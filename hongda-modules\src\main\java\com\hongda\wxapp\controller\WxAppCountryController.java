package com.hongda.wxapp.controller;

import com.alibaba.fastjson2.JSON;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.content.domain.HongdaCountry;
import com.hongda.content.domain.HongdaPark;
import com.hongda.content.service.IHongdaCountryService;
import com.hongda.content.service.IHongdaParkService;
import com.hongda.wxapp.domain.vo.CountryDetailVO;
import com.hongda.wxapp.domain.vo.CountryListVO;
import com.hongda.wxapp.domain.vo.IndustrialParkVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import com.hongda.common.annotation.Anonymous;

/**
 * 小程序端 - 国别信息接口 (最终优化版)
 *
 * <AUTHOR> AI
 * @date 2025-07-29
 */
@Anonymous
@Tag(name = "小程序国别信息接口", description = "为小程序提供国别信息查询功能")
@RestController
@RequestMapping("/api/v1/country")
public class WxAppCountryController extends BaseController {

    @Autowired
    private IHongdaCountryService hongdaCountryService;

    @Autowired(required = false)
    private IHongdaParkService hongdaParkService;

    @Operation(summary = "获取国别列表", description = "根据大洲和搜索关键词查询国别列表")
    @GetMapping("/list")
    public AjaxResult getCountryList(
            // 【核心修正】明确声明接收前端的这两个参数
            @RequestParam(required = false) String continent,
            @RequestParam(required = false) String keyword
    ) {
        HongdaCountry queryParams = new HongdaCountry();

        // 业务规则：小程序端只查询状态为 "0" (显示) 的国别信息
        queryParams.setStatus("0");

        // 如果前端传来 "ALL"，则将其设为null，表示不按大洲筛选
        if ("ALL".equalsIgnoreCase(continent)) {
            queryParams.setContinent(null);
        } else {
            queryParams.setContinent(continent);
        }

        // 【核心修正】将接收到的keyword设置到用于查询的nameCn字段上
        // Mapper.xml中的SQL会用这个字段去同时匹配name_cn和name_en
        queryParams.setNameCn(keyword);

        List<HongdaCountry> countryList = hongdaCountryService.selectHongdaCountryList(queryParams);

        // 将实体类列表映射为VO列表
        List<CountryListVO> voList = countryList.stream().map(country -> {
            CountryListVO vo = new CountryListVO();
            BeanUtils.copyProperties(country, vo);
            return vo;
        }).collect(Collectors.toList());

        return AjaxResult.success(voList);
    }

    @Operation(summary = "获取国别详情", description = "根据ID查询单个国别详细信息，并聚合其下属园区信息")
    @GetMapping("/{id}")
    public AjaxResult getCountryDetail(@Parameter(description = "国别ID") @PathVariable("id") Long id) {
        HongdaCountry country = hongdaCountryService.selectHongdaCountryById(id);

        if (country == null || !"0".equals(country.getStatus())) {
            return AjaxResult.error("该国别信息不存在或已下线");
        }

        CountryDetailVO detailVO = new CountryDetailVO();
        BeanUtils.copyProperties(country, detailVO);

        try {
            // 【已修正】将 getBasicInfo_json() 修改为 getBasicInfoJson()
            if (country.getBasicInfoJson() != null && !country.getBasicInfoJson().isEmpty()) {
                detailVO.setBasicInfoJson(JSON.parse(country.getBasicInfoJson()));
            }
        } catch (Exception e) {
            detailVO.setBasicInfoJson(country.getBasicInfoJson());
        }

        if (hongdaParkService != null) {
            HongdaPark parkQuery = new HongdaPark();
            parkQuery.setCountryId(id);
            parkQuery.setStatus("0");
            List<HongdaPark> parks = hongdaParkService.selectHongdaParkList(parkQuery);

            if (parks != null) {
                List<IndustrialParkVO> parkVOs = parks.stream().map(park -> {
                    IndustrialParkVO parkVO = new IndustrialParkVO();
                    parkVO.setId(park.getId());
                    parkVO.setName(park.getName());
                    parkVO.setLocation(park.getLocation());
                    parkVO.setIndustries(park.getMainIndustries());
                    parkVO.setFeatures(park.getSummary());
                    parkVO.setCoverImageUrl(park.getCoverImageUrl());
                    return parkVO;
                }).collect(Collectors.toList());
                detailVO.setIndustrialParks(parkVOs);
            } else {
                detailVO.setIndustrialParks(Collections.emptyList());
            }
        }

        return AjaxResult.success(detailVO);
    }
}