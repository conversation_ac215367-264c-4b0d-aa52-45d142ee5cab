<view class="content-module-container data-v-4629d60d"><view class="sub-tabs-container data-v-4629d60d"><scroll-view class="sub-tabs-wrapper data-v-4629d60d" scroll-x="true" show-scrollbar="{{false}}"><view wx:for="{{a}}" wx:for-item="category" wx:key="b" class="{{['sub-tab-item', 'data-v-4629d60d', category.c && 'active']}}" bindtap="{{category.d}}">{{category.a}}</view></scroll-view><view class="divider data-v-4629d60d"></view></view><view class="article-list-container data-v-4629d60d"><view wx:if="{{b}}" class="loading-state data-v-4629d60d"><u-loading-icon wx:if="{{c}}" class="data-v-4629d60d" u-i="4629d60d-0" bind:__l="__l" u-p="{{c}}"></u-loading-icon></view><view wx:elif="{{d}}" class="article-list data-v-4629d60d"><view wx:for="{{e}}" wx:for-item="article" wx:key="c" class="article-item data-v-4629d60d" bindtap="{{article.d}}"><view class="dot data-v-4629d60d"></view><text class="article-title data-v-4629d60d">{{article.a}}</text><u-icon wx:if="{{f}}" class="data-v-4629d60d" u-i="{{article.b}}" bind:__l="__l" u-p="{{f}}"></u-icon></view></view><view wx:else class="empty-state data-v-4629d60d"><u-empty wx:if="{{g}}" class="data-v-4629d60d" u-i="4629d60d-2" bind:__l="__l" u-p="{{g}}"></u-empty></view></view></view>