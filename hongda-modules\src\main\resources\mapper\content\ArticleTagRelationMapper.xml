<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.ArticleTagRelationMapper">

    <insert id="batchArticleTag">
        insert into hongda_article_tag_relation(article_id, tag_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.articleId},#{item.tagId})
        </foreach>
    </insert>

    <delete id="deleteArticleTagByArticleId" parameterType="Long">
        delete from hongda_article_tag_relation where article_id = #{articleId}
    </delete>

    <select id="selectTagIdsByArticleId" parameterType="Long" resultType="Long">
        select tag_id from hongda_article_tag_relation where article_id = #{articleId}
    </select>

    <select id="selectUsageCountByTagIds" resultType="Long">
        select count(*) from hongda_article_tag_relation
        where tag_id in
        <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>

    <select id="countArticlesByTagId" resultType="Integer">
        select count(*) from hongda_article_tag_relation where tag_id = #{tagId}
    </select>

</mapper>