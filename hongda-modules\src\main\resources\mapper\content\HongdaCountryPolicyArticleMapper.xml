<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaCountryPolicyArticleMapper">

    <resultMap type="HongdaCountryPolicyArticle" id="HongdaCountryPolicyArticleResult">
        <result property="articleId"    column="article_id"    />
        <result property="countryId"    column="country_id"    />
        <result property="policyType"    column="policy_type"    />
        <result property="title"    column="title"    />
        <result property="summary"    column="summary"    />
        <result property="content"    column="content"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="status"    column="status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="countryName"   column="countryName"    />
        <collection property="tags" javaType="java.util.List" resultMap="com.hongda.content.mapper.HongdaCountryPolicyTagMapper.HongdaCountryPolicyTagResult"/>
    </resultMap>

    <sql id="selectHongdaCountryPolicyArticleVo">
        select
            p.article_id, p.country_id, p.policy_type, p.title, p.summary, p.content, p.cover_image, p.status, p.sort_order, p.create_by, p.create_time, p.update_by, p.update_time, p.remark,
            c.name_cn as countryName,
            t.tag_id, t.tag_name, t.sort_order as tag_sort_order
        from hongda_country_policy_article p
                 left join hongda_country c on p.country_id = c.id
                 left join hongda_country_policy_article_tag_relation r on p.article_id = r.article_id
                 left join hongda_country_policy_tag t on r.tag_id = t.tag_id
    </sql>

    <select id="selectHongdaCountryPolicyArticleList" parameterType="HongdaCountryPolicyArticle" resultMap="HongdaCountryPolicyArticleResult">
        select
        p.article_id, p.country_id, p.policy_type, p.title, p.summary, p.content, p.cover_image, p.status, p.sort_order, p.create_by, p.create_time, p.update_by, p.update_time, p.remark,
        c.name_cn as countryName
        from hongda_country_policy_article p
        left join hongda_country c on p.country_id = c.id
        <where>
            <if test="countryId != null "> and p.country_id = #{countryId}</if>
            <if test="policyType != null  and policyType != ''"> and p.policy_type = #{policyType}</if>
            <if test="title != null  and title != ''"> and p.title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and p.status = #{status}</if>
        </where>
        order by p.sort_order asc, p.create_time desc
    </select>

    <select id="selectHongdaCountryPolicyArticleByArticleId" parameterType="Long" resultMap="HongdaCountryPolicyArticleResult">
        <include refid="selectHongdaCountryPolicyArticleVo"/>
        where p.article_id = #{articleId}
    </select>

    <insert id="insertHongdaCountryPolicyArticle" parameterType="HongdaCountryPolicyArticle" useGeneratedKeys="true" keyProperty="articleId">
        insert into hongda_country_policy_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="countryId != null">country_id,</if>
            <if test="policyType != null and policyType != ''">policy_type,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="summary != null">summary,</if>
            <if test="content != null">content,</if>
            <if test="coverImage != null">cover_image,</if>
            <if test="status != null">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="countryId != null">#{countryId},</if>
            <if test="policyType != null and policyType != ''">#{policyType},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="summary != null">#{summary},</if>
            <if test="content != null">#{content},</if>
            <if test="coverImage != null">#{coverImage},</if>
            <if test="status != null">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHongdaCountryPolicyArticle" parameterType="HongdaCountryPolicyArticle">
        update hongda_country_policy_article
        <trim prefix="SET" suffixOverrides=",">
            <if test="countryId != null">country_id = #{countryId},</if>
            <if test="policyType != null and policyType != ''">policy_type = #{policyType},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="content != null">content = #{content},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where article_id = #{articleId}
    </update>

    <delete id="deleteHongdaCountryPolicyArticleByArticleId" parameterType="Long">
        delete from hongda_country_policy_article where article_id = #{articleId}
    </delete>

    <delete id="deleteHongdaCountryPolicyArticleByArticleIds" parameterType="String">
        delete from hongda_country_policy_article where article_id in
        <foreach item="articleId" collection="array" open="(" separator="," close=")">
            #{articleId}
        </foreach>
    </delete>

    <delete id="deleteCountryPolicyArticleTagByArticleId" parameterType="Long">
        delete from hongda_country_policy_article_tag_relation where article_id = #{articleId}
    </delete>

    <insert id="batchCountryPolicyArticleTag">
        insert into hongda_country_policy_article_tag_relation(article_id, tag_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.articleId},#{item.tagId})
        </foreach>
    </insert>
</mapper>