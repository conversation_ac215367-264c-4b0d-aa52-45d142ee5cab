// 文件路径: src/main/java/com/hongda/content/mapper/ArticleTagRelationMapper.java
package com.hongda.content.mapper;

import com.hongda.content.domain.ArticleTagRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param; // [修改] 建议添加 @Param 注解，让参数名更清晰

import java.util.List;

/**
 * 文章标签关联表 数据层
 * <AUTHOR>
 */
@Mapper
public interface ArticleTagRelationMapper
{
    /**
     * 批量新增文章标签信息
     */
    public int batchArticleTag(List<ArticleTagRelation> articleTagList);

    /**
     * 通过文章ID删除文章标签关联
     */
    public int deleteArticleTagByArticleId(Long articleId);

    /**
     * 根据文章ID查询所有关联的标签ID
     */
    public List<Long> selectTagIdsByArticleId(Long articleId);

    /**
     * 根据一组标签ID，查询这些标签被文章使用的总次数
     * (用于批量删除的检查)
     */
    public Long selectUsageCountByTagIds(@Param("tagIds") Long[] tagIds); // [修改] 添加 @Param 注解

    /**
     * [新增] 根据单个标签ID，查询该标签被多少篇文章使用
     * (用于单个删除的检查，可以返回更详细的错误信息)
     *
     * @param tagId 标签ID
     * @return 关联的文章数量
     */
    public int countArticlesByTagId(@Param("tagId") Long tagId);
}