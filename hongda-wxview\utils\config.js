/**
 * 项目配置文件
 */

// 开发环境配置
const development = {
  baseUrl: 'http://localhost:80', // 开发环境API基础URL
  imageBaseUrl: '', // 【关键修正】设置为空，因为后端返回的是完整的OSS签名URL
  timeout: 10000, // 请求超时时间
  debug: true // 是否开启调试模式
};

// 生产环境配置
const production = {
  baseUrl: 'https://wx.hongdashuiwu.com', // 生产环境API基础URL
  imageBaseUrl: '', // 【关键修正】生产环境同样设置为空
  timeout: 10000, // 请求超时时间
  debug: false // 是否开启调试模式
};

// 根据环境变量选择配置
// 优先级：production > testing > development
const config = process.env.NODE_ENV === 'production' 
  ? production 
  : process.env.NODE_ENV === 'testing' 
    ? testing 
    : development;

// API路径配置 - 小程序专用接口（不包含/api/v1前缀，因为request.js已经添加了）
export const API_PATHS = {
  EVENT_LIST: '/events/list',
  EVENT_DETAIL: '/events',
  EVENT_EXPORT: '/content/pages_event/export',
  EVENT_LOCATIONS: '/events/locations',
  EVENT_HOT: '/events/hot',
  EVENT_UPCOMING: '/events/upcoming',
  EVENT_SEARCH: '/events/search',
  EVENT_BY_LOCATION: '/events/by-location',
  EVENT_LOCATION_STATS: '/events/location-stats'
};

// 分页配置
export const PAGE_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 50
};

// 导出基础URL便于其他模块使用
export const BASE_URL = config.baseUrl;
export const IMAGE_BASE_URL = config.imageBaseUrl;

export default config;