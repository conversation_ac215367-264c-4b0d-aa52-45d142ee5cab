// 文件路径: src/main/java/com/hongda/content/domain/HongdaCountryPolicyArticleTag.java
package com.hongda.content.domain;

/**
 * 文章与政策标签关联对象
 * <p>
 * 这是一个数据传输对象(DTO)，用于在Service层和Mapper层之间传递文章ID和标签ID的配对信息，
 * 以便进行批量插入操作。
 * </p>
 * <AUTHOR>
 */
public class HongdaCountryPolicyArticleTag
{
    /** 文章ID */
    private Long articleId;

    /** 标签ID */
    private Long tagId;

    public HongdaCountryPolicyArticleTag() {
    }

    public HongdaCountryPolicyArticleTag(Long articleId, Long tagId) {
        this.articleId = articleId;
        this.tagId = tagId;
    }

    // --- Getters 和 Setters ---

    public Long getArticleId() {
        return articleId;
    }

    public void setArticleId(Long articleId) {
        this.articleId = articleId;
    }

    public Long getTagId() {
        return tagId;
    }

    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }
}