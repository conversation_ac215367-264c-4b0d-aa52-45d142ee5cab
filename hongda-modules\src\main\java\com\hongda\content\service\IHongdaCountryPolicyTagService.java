package com.hongda.content.service;

import java.util.List;
import com.hongda.content.domain.HongdaCountryPolicyTag;

/**
 * 政策标签管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface IHongdaCountryPolicyTagService 
{
    /**
     * 查询政策标签管理
     * 
     * @param tagId 政策标签管理主键
     * @return 政策标签管理
     */
    public HongdaCountryPolicyTag selectHongdaCountryPolicyTagByTagId(Long tagId);

    /**
     * 查询政策标签管理列表
     * 
     * @param hongdaCountryPolicyTag 政策标签管理
     * @return 政策标签管理集合
     */
    public List<HongdaCountryPolicyTag> selectHongdaCountryPolicyTagList(HongdaCountryPolicyTag hongdaCountryPolicyTag);

    /**
     * 新增政策标签管理
     * 
     * @param hongdaCountryPolicyTag 政策标签管理
     * @return 结果
     */
    public int insertHongdaCountryPolicyTag(HongdaCountryPolicyTag hongdaCountryPolicyTag);

    /**
     * 修改政策标签管理
     * 
     * @param hongdaCountryPolicyTag 政策标签管理
     * @return 结果
     */
    public int updateHongdaCountryPolicyTag(HongdaCountryPolicyTag hongdaCountryPolicyTag);

    /**
     * 批量删除政策标签管理
     * 
     * @param tagIds 需要删除的政策标签管理主键集合
     * @return 结果
     */
    public int deleteHongdaCountryPolicyTagByTagIds(Long[] tagIds);

    /**
     * 删除政策标签管理信息
     * 
     * @param tagId 政策标签管理主键
     * @return 结果
     */
    public int deleteHongdaCountryPolicyTagByTagId(Long tagId);
}
