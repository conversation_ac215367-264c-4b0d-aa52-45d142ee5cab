package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.platform.domain.HongdaPage;
import com.hongda.platform.service.IHongdaPageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Anonymous;

@Anonymous
@Tag(name = "小程序页面接口", description = "小程序端自定义页面相关接口")
@RestController
@RequestMapping("/api/v1/page")
public class WxAppPageController extends BaseController {

    @Autowired
    private IHongdaPageService pageService;

    @Operation(summary = "获取自定义页面详情", description = "根据页面ID获取其富文本内容等信息")
    @GetMapping("/{id}")
    public AjaxResult getPageDetails(
            @Parameter(description = "页面ID", example = "1") @PathVariable("id") Long id) {

        HongdaPage page = pageService.selectHongdaPageById(id);

        // 检查页面是否存在或是否处于启用状态
        if (page == null || page.getStatus() == 0) {
            return AjaxResult.error("页面不存在或已禁用");
        }

        return AjaxResult.success(page);
    }
}