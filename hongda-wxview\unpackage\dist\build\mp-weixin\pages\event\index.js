"use strict";const e=require("../../common/vendor.js"),a=require("../../api/data/event.js"),l=require("../../utils/tools.js"),o=require("../../utils/date.js"),t=require("../../utils/config.js");if(!Array){(e.resolveComponent("up-subsection")+e.resolveComponent("up-icon")+e.resolveComponent("up-empty")+e.resolveComponent("up-button")+e.resolveComponent("up-loadmore"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-subsection/u-subsection.js")+v+(()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../uni_modules/uview-plus/components/u-button/u-button.js")+n+(()=>"../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js")+s+u)();const u=()=>"../../components/layout/CustomTabBar.js",v=()=>"../../components/home/<USER>",n=()=>"../../components/event/EventCard.js",s=()=>"../../components/event/EventCalendarTimeline.js",r={__name:"index",setup(u){const v=e.ref(0),n=e.ref(""),s=e.ref([]),r=e.ref(!1),i=e.ref(!1),c=e.ref(!1),g=e.ref(""),m=e.ref({pageNum:1,pageSize:t.PAGE_CONFIG.DEFAULT_PAGE_SIZE,total:0,hasMore:!0}),d=e.ref({sortBy:1,location:1,timeRange:1,status:1}),p=e.ref({location:1,timeRange:1}),f=e.ref({sortBy:1,location:1,timeRange:1,status:1}),b=e.ref({location:1,timeRange:1}),h=e.ref(!1),y=e.ref(!1),w=e.ref(!1),S=e.ref(!1),R=e.ref("60rpx"),x=e.ref([{label:"综合排序",value:1},{label:"最新发布",value:2},{label:"最近开始",value:3}]),A=e.ref({label:"全部地区",value:1}),D=e.ref([{label:"北京",value:2},{label:"上海",value:3},{label:"广州",value:4},{label:"深圳",value:5}]),B=e.ref([]),C=e.computed((()=>[A.value,...D.value,...B.value.map(((e,a)=>({label:e,value:100+a})))])),j=e.ref([{label:"全部时间",value:1},{label:"1周内",value:2},{label:"1月内",value:3},{label:"1年内",value:4}]),T=e.ref([{label:"全部状态",value:1},{label:"即将开始",value:2},{label:"报名中",value:3},{label:"报名截止",value:4}]),_=e.computed((()=>r.value?"loading":m.value.hasMore?"more":"nomore")),z=e.computed((()=>{if(!s.value||0===s.value.length)return[];const e=new Map,a=["周日","周一","周二","周三","周四","周五","周六"];return s.value.forEach((l=>{const t=o.parseDate(l.startTime),u=t.getFullYear(),v=(new Date).getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0");let r;r=u!==v?`${u}年${n}月${s}日`:`${n}.${s}`;const i=String(t.getMonth()+1).padStart(2,"0"),c=String(t.getDate()).padStart(2,"0"),g=`${t.getFullYear()}-${i}-${c}`;e.has(g)||e.set(g,{date:g,formattedDate:r,dayOfWeek:a[t.getDay()],events:[]}),e.get(g).events.push(l)})),Array.from(e.values())})),M=e.computed((()=>{let e=0;const a=[];for(const l of z.value){if(e>=10)break;const o=10-e,t=l.events.slice(0,o);a.push({...l,events:t}),e+=t.length}return a})),E=e.computed((()=>z.value.reduce(((e,a)=>e+a.events.length),0)>10)),$=e=>{const a=new Date;let l=null,o=null;switch(e){case 2:l=a,o=new Date(a.getTime()+6048e5),console.log("时间筛选: 1周内 -",l.toLocaleDateString(),"到",o.toLocaleDateString());break;case 3:l=a,o=new Date(a.getFullYear(),a.getMonth()+1,a.getDate()),console.log("时间筛选: 1月内 -",l.toLocaleDateString(),"到",o.toLocaleDateString());break;case 4:l=a,o=new Date(a.getFullYear()+1,a.getMonth(),a.getDate()),console.log("时间筛选: 1年内 -",l.toLocaleDateString(),"到",o.toLocaleDateString());break;default:return console.log("时间筛选: 全部时间"),null}return{timeRangeStart:l.toISOString(),timeRangeEnd:o.toISOString()}},N=async(l=!1)=>{if(!r.value){r.value=!0;try{const e=((e=!1)=>{const a={pageNum:e?m.value.pageNum:1,pageSize:m.value.pageSize};n.value.trim()&&(a.title=n.value.trim());const l=1===v.value?p.value:d.value;if(l.location>1){const e={2:"北京",3:"上海",4:"广州",5:"深圳"};if(e[l.location])a.location=e[l.location];else if(l.location>=100){const e=l.location-100;e<B.value.length&&(a.location=B.value[e])}}if(0===v.value&&l.status>1){const e={2:0,3:1,4:2};e.hasOwnProperty(l.status)?a.registrationStatus=e[l.status]:console.warn("未知的报名状态筛选值:",l.status)}if(1===v.value)a.orderBy="startTime",a.isAsc="asc";else switch(l.sortBy){case 1:a.orderBy="comprehensive";break;case 2:a.orderBy="startTime",a.isAsc="asc",console.log("按时间排序: 最近开始优先");break;case 3:a.orderBy="createTime",a.isAsc="desc",console.log("按最新发布排序: 最新创建的活动优先");break;default:a.orderBy="createTime",a.isAsc="desc",console.log("默认排序: 最新发布")}if(l.timeRange>1){const e=$(l.timeRange);e&&Object.assign(a,e)}return a})(l);let o;console.log("请求参数:",e),o=1===v.value?await a.getCalendarEventsApi(e):await a.getEventListApi(e);const{rows:t=[],total:u=0}=o;l?s.value.push(...t):(s.value=t,m.value.pageNum=1),m.value.total=u,m.value.hasMore=s.value.length<u,c.value=!1,console.log(`获取活动列表成功: ${t.length} 条记录, 总计: ${u}`)}catch(o){console.error("获取活动列表失败:",o);let a="获取活动列表失败";o.message&&o.message.includes("timeout")?a="网络请求超时，请重试":o.message&&o.message.includes("Network")&&(a="网络连接失败，请检查网络"),e.index.showToast({title:a,icon:"none",duration:3e3}),l||0!==s.value.length||(c.value=!0)}finally{r.value=!1,i.value=!1}}},k=l.debounce((()=>{N()}),500),L=e.computed((()=>{const e=x.value.find((e=>e.value===d.value.sortBy));return e?e.label:"综合排序"})),F=e.computed((()=>{const e=T.value.find((e=>e.value===d.value.status));return e?e.label:"全部状态"})),O=e.computed((()=>{const e=C.value.find((e=>e.value===d.value.location));return e?e.label:"热门地区"})),q=e.computed((()=>{const e=j.value.find((e=>e.value===d.value.timeRange));return e?e.label:"全部时间"})),I=e.computed((()=>{const e=C.value.find((e=>e.value===p.value.location));return e?e.label:"热门地区"})),P=e.computed((()=>{const e=j.value.find((e=>e.value===p.value.timeRange));return e?e.label:"全部时间"})),Y=e=>{v.value=e,s.value=[],m.value.pageNum=1,m.value.hasMore=!0,N()},G=e=>{n.value=e,k()},U=()=>{console.log("筛选条件变更，重置数据并重新加载"),s.value=[],m.value.pageNum=1,m.value.hasMore=!0,N()},H=()=>{h.value=!h.value,y.value=!1,w.value=!1,S.value=!1},J=()=>{y.value=!y.value,h.value=!1,w.value=!1,S.value=!1,R.value="60rpx"},K=()=>{w.value=!w.value,h.value=!1,y.value=!1,S.value=!1,R.value="240rpx"},V=()=>{S.value=!S.value,h.value=!1,y.value=!1,w.value=!1},W=e=>{1===v.value?b.value.location=e:f.value.location=e,console.log("临时选择地区:",e)},Z=e=>{1===v.value?b.value.timeRange=e:f.value.timeRange=e,console.log("临时选择时间:",e)},Q=()=>{f.value.sortBy=1,d.value.sortBy=1,h.value=!1,console.log("重置排序筛选为初始状态"),U()},X=()=>{d.value.sortBy=f.value.sortBy,h.value=!1,console.log("应用排序筛选:",f.value.sortBy),U()},ee=()=>{1===v.value?(b.value.location=1,p.value.location=1):(f.value.location=1,d.value.location=1),y.value=!1,console.log("重置地区筛选为初始状态"),U()},ae=()=>{1===v.value?p.value.location=b.value.location:d.value.location=f.value.location,y.value=!1,console.log("应用地区筛选:",1===v.value?b.value.location:f.value.location),U(),R.value="60rpx"},le=()=>{1===v.value?(b.value.timeRange=1,p.value.timeRange=1):(f.value.timeRange=1,d.value.timeRange=1),w.value=!1,console.log("重置时间筛选为初始状态"),U()},oe=()=>{1===v.value?p.value.timeRange=b.value.timeRange:d.value.timeRange=f.value.timeRange,w.value=!1,console.log("应用时间筛选:",1===v.value?b.value.timeRange:f.value.timeRange),U(),R.value="240rpx"},te=()=>{f.value.status=1,d.value.status=1,S.value=!1,console.log("重置状态筛选为初始状态"),U()},ue=()=>{d.value.status=f.value.status,S.value=!1,console.log("应用状态筛选:",f.value.status),U()},ve=a=>{e.index.navigateTo({url:`/pages_sub/pages_event/detail?id=${a.id}`})},ne=()=>{v.value=0,N()},se=()=>{i.value=!0,m.value.pageNum=1,N()},re=()=>{m.value.hasMore&&!r.value&&(m.value.pageNum++,N(!0))};return e.onLoad((()=>{const l=e.index.getStorageSync("staticAssets");g.value=(null==l?void 0:l.eventbg)||"",(async()=>{try{const l=await a.getEventCitiesApi();let o=null;if(Array.isArray(l)?o=l:(l&&Array.isArray(l.data)||l&&200===l.code&&Array.isArray(l.data))&&(o=l.data),console.log("提取的城市列表:",o),o&&Array.isArray(o)){const e=D.value.map((e=>e.label)),a=o.filter((a=>a&&a.trim()&&!e.includes(a.trim())));B.value=a}else e.index.showToast({title:"城市数据格式错误",icon:"none",duration:2e3})}catch(l){console.error("获取城市列表失败:",l),e.index.showToast({title:"获取城市列表失败",icon:"none",duration:2e3})}})(),N(),e.index.$on("dataChanged",(()=>{console.log("活动列表页收到数据变化事件，刷新列表..."),N(),e.index.showToast({title:"列表已更新",icon:"success",duration:1500})}))})),e.onShow((()=>{e.index.hideTabBar()})),e.onUnload((()=>{e.index.$off("dataChanged")})),e.onReachBottom((()=>{re()})),e.onPullDownRefresh((()=>{se(),setTimeout((()=>{e.index.stopPullDownRefresh()}),1e3)})),(a,l)=>e.e({a:g.value,b:e.o(Y),c:e.p({list:["列表","日历"],current:v.value,mode:"subsection",activeColor:"#f56c6c"}),d:e.o(G),e:e.o(e.unref(k)),f:e.o((e=>n.value=e)),g:e.p({placeholder:"搜索活动",modelValue:n.value}),h:0===v.value},0===v.value?e.e({i:e.t(L.value),j:h.value?1:"",k:e.p({name:"arrow-down",size:"14",color:"#666"}),l:e.o(H),m:e.t(O.value),n:y.value?1:"",o:e.p({name:"arrow-down",size:"14",color:"#666"}),p:e.o(J),q:e.t(q.value),r:w.value?1:"",s:e.p({name:"arrow-down",size:"14",color:"#666"}),t:e.o(K),v:e.t(F.value),w:S.value?1:"",x:e.p({name:"arrow-down",size:"14",color:"#666"}),y:e.o(V),z:h.value},h.value?{A:e.f(x.value,((a,l,o)=>({a:e.t(a.label),b:a.value,c:e.n({active:f.value.sortBy===a.value}),d:e.o((e=>{return l=a.value,f.value.sortBy=l,void console.log("临时选择排序:",l);var l}),a.value)}))),B:e.o(Q),C:e.o(X)}:{},{D:y.value},y.value?e.e({E:e.t(A.value.label),F:e.n({active:(1===v.value?b.value.location:f.value.location)===A.value.value}),G:e.o((e=>W(A.value.value))),H:e.f(D.value,((a,l,o)=>({a:e.t(a.label),b:a.value,c:e.n({active:(1===v.value?b.value.location:f.value.location)===a.value}),d:e.o((e=>W(a.value)),a.value)}))),I:B.value.length>0},B.value.length>0?{J:e.f(B.value,((a,l,o)=>({a:e.t(a),b:100+l,c:e.n({active:(1===v.value?b.value.location:f.value.location)===100+l}),d:e.o((e=>W(100+l)),100+l)})))}:{},{K:e.o(ee),L:e.o(ae)}):{},{M:w.value},w.value?{N:e.f(j.value,((a,l,o)=>({a:e.t(a.label),b:a.value,c:e.n({active:(1===v.value?b.value.timeRange:f.value.timeRange)===a.value}),d:e.o((e=>Z(a.value)),a.value)}))),O:e.o(le),P:e.o(oe)}:{},{Q:S.value},S.value?{R:e.f(T.value,((a,l,o)=>({a:e.t(a.label),b:a.value,c:e.n({active:f.value.status===a.value}),d:e.o((e=>{return l=a.value,f.value.status=l,void console.log("临时选择状态:",l);var l}),a.value)}))),S:e.o(te),T:e.o(ue)}:{}):{},{U:0===v.value},0===v.value?e.e({V:!r.value&&0===s.value.length},r.value||0!==s.value.length?{}:e.e({W:e.p({mode:"data",text:"暂无活动数据",textColor:"#909399",iconSize:"120"}),X:c.value},c.value?{Y:e.o(N),Z:e.p({type:"primary",size:"normal"})}:{}),{aa:e.f(s.value,((a,l,o)=>({a:a.id,b:e.o((e=>ve(a)),a.id),c:"fce3c2f9-8-"+o,d:e.p({event:a})}))),ab:e.p({status:_.value,"loading-text":"正在加载...","loadmore-text":"上拉加载更多","nomore-text":"没有更多了"}),ac:e.o(re),ad:i.value,ae:e.o(se)}):{},{af:1===v.value},1===v.value?e.e({ag:e.t(I.value),ah:y.value?1:"",ai:e.p({name:"arrow-down",size:"14",color:"#666"}),aj:e.o(J),ak:e.t(P.value),al:w.value?1:"",am:e.p({name:"arrow-down",size:"14",color:"#666"}),an:e.o(K),ao:y.value},y.value?e.e({ap:e.t(A.value.label),aq:e.n({active:(1===v.value?b.value.location:f.value.location)===A.value.value}),ar:e.o((e=>W(A.value.value))),as:e.f(D.value,((a,l,o)=>({a:e.t(a.label),b:a.value,c:e.n({active:(1===v.value?b.value.location:f.value.location)===a.value}),d:e.o((e=>W(a.value)),a.value)}))),at:B.value.length>0},B.value.length>0?{av:e.f(B.value,((a,l,o)=>({a:e.t(a),b:100+l,c:e.n({active:(1===v.value?b.value.location:f.value.location)===100+l}),d:e.o((e=>W(100+l)),100+l)})))}:{},{aw:e.o(ee),ax:e.o(ae)}):{},{ay:w.value},w.value?{az:e.f(j.value,((a,l,o)=>({a:e.t(a.label),b:a.value,c:e.n({active:(1===v.value?b.value.timeRange:f.value.timeRange)===a.value}),d:e.o((e=>Z(a.value)),a.value)}))),aA:e.o(le),aB:e.o(oe)}:{}):{},{aC:1===v.value},1===v.value?e.e({aD:!r.value&&0===M.value.length},r.value||0!==M.value.length?{aI:e.o(ve),aJ:e.o(ne),aK:e.p({groups:M.value,"has-more":E.value,"notch-left":R.value})}:e.e({aE:e.p({mode:"data",text:"暂无活动数据",textColor:"#909399",iconSize:"120"}),aF:c.value},c.value?{aG:e.o(N),aH:e.p({type:"primary",size:"normal"})}:{}),{aL:e.o(re),aM:i.value,aN:e.o(se)}):{},{aO:e.p({current:2})})}},i=e._export_sfc(r,[["__scopeId","data-v-fce3c2f9"]]);wx.createPage(i);
