"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/image.js"),o={__name:"EventCalendarTimeline",props:{groups:{type:Array,required:!0},hasMore:{type:Boolean,default:!1},notchLeft:{type:String,default:"60rpx"}},setup(o){const r=e.ref("");e.onMounted((()=>{const t=e.index.getStorageSync("staticAssets");t&&(r.value=t["golden-location"]||t.golden_location||"")}));const n=e=>e.city&&e.city.trim()?e.city.trim().replace(/市$/,""):"待定";return(a,i)=>e.e({a:o.notchLeft,b:e.f(o.groups,((r,i,c)=>e.e({a:e.t(r.formattedDate),b:e.t(r.dayOfWeek),c:i<o.groups.length-1},(o.groups.length,{}),{d:e.f(r.events,((o,r,i)=>({a:e.unref(t.getFullImageUrl)(o.iconUrl),b:e.t(o.title),c:e.t(n(o)),d:o.id,e:e.o((e=>a.$emit("click-item",o)),o.id)}))),e:r.date}))),c:r.value,d:o.hasMore},o.hasMore?{e:e.o((e=>a.$emit("view-more")))}:{})}},r=e._export_sfc(o,[["__scopeId","data-v-6149846b"]]);wx.createComponent(r);
