/* uview-plus样式已通过easycom按需加载，无需全局引入 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-ebaf798e {
  height: 100%;
  background-color: #f4f4f4;
}

/* [核心修改] 移除了所有 .skeleton- 相关样式，其余样式保持不变 */
.page-container.data-v-ebaf798e {
  height: 100vh;
  background-color: #FFFFFF;
}
.fixed-header.data-v-ebaf798e {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: #FFFFFF;
}
.scrollable-content.data-v-ebaf798e {
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.error-state.data-v-ebaf798e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #FFFFFF;
}
.error-state .empty-icon.data-v-ebaf798e {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}
.error-state .empty-title.data-v-ebaf798e {
  font-size: 30rpx;
  font-weight: 500;
  color: #606266;
  margin-bottom: 12rpx;
}
.error-state .empty-desc.data-v-ebaf798e {
  font-size: 26rpx;
  color: #909399;
}
.error-state .retry-btn.data-v-ebaf798e {
  margin-top: 40rpx;
  padding: 0 60rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #3c9cff;
  border-radius: 35rpx;
}
.status-bar.data-v-ebaf798e {
  width: 100%;
}
.custom-nav-bar.data-v-ebaf798e {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: content-box;
}
.nav-back-button.data-v-ebaf798e {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 15rpx;
}
.nav-title.data-v-ebaf798e {
  font-size: 34rpx;
  font-weight: bold;
  color: #000000;
}
.hero-section.data-v-ebaf798e {
  position: relative;
  width: 100%;
  height: 500rpx;
}
.hero-section .hero-image.data-v-ebaf798e {
  width: 100%;
  height: 100%;
}
.main-content.data-v-ebaf798e {
  padding: 0;
  margin-top: 0;
  position: relative;
  z-index: 3;
}
.article-title.data-v-ebaf798e {
  font-size: 40rpx;
  color: #23232A;
  font-weight: 700;
  line-height: 1.5;
  padding: 30rpx 30rpx 0 30rpx;
  margin-bottom: 24rpx;
}
.article-meta-new.data-v-ebaf798e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}
.article-meta-new .meta-left.data-v-ebaf798e {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.article-meta-new .meta-text.data-v-ebaf798e {
  font-size: 24rpx;
  color: #9B9A9A;
}
.summary-card.data-v-ebaf798e {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin: 0 30rpx 0rpx 30rpx;
  padding: 30rpx;
}
.summary-card .summary-text.data-v-ebaf798e {
  font-size: 30rpx;
  font-weight: 600;
  color: #495057;
  line-height: 1.8;
}
.content-card.data-v-ebaf798e {
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 0 30rpx 0 30rpx;
}
.tags-section-new.data-v-ebaf798e {
  margin: 0 30rpx 0 30rpx;
  padding: 20rpx 24rpx;
  background: #F2F4FA;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8rpx;
}
.tags-section-new .tags-label.data-v-ebaf798e {
  font-size: 24rpx;
  color: #66666E;
  font-weight: 600;
  margin-right: 12rpx;
}
.tags-section-new .tag-item-new.data-v-ebaf798e {
  font-size: 24rpx;
  color: #66666E;
}
.comment-container.data-v-ebaf798e {
  margin-top: 20rpx;
  padding: 0 30rpx 40rpx 30rpx;
  background: #ffffff;
}
.comment-header-section.data-v-ebaf798e {
  padding: 40rpx 0 30rpx 0;
  border-bottom: 2rpx solid #f1f5f9;
}
.comment-header-section .comment-main-title.data-v-ebaf798e {
  font-size: 36rpx;
  font-weight: 600;
  color: #303133;
}
.comment-input-card.data-v-ebaf798e {
  width: 702rpx;
  height: 72rpx;
  background: #F7F7F7;
  border-radius: 8rpx;
  margin: 30rpx 0;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  box-sizing: border-box;
}
.comment-input-card .comment-input-icon.data-v-ebaf798e {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}
.comment-input-card .comment-input-placeholder.data-v-ebaf798e {
  font-size: 28rpx;
  color: #9B9A9A;
}
.comment-list-container.data-v-ebaf798e {
  padding-top: 20rpx;
}
.empty-state.data-v-ebaf798e {
  padding: 80rpx 0;
  text-align: center;
}
.empty-state .empty-icon.data-v-ebaf798e {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}
.empty-state .empty-title.data-v-ebaf798e {
  font-size: 30rpx;
  font-weight: 500;
  color: #606266;
  margin-bottom: 12rpx;
}
.empty-state .empty-desc.data-v-ebaf798e {
  font-size: 26rpx;
  color: #909399;
}
.comment-popup.data-v-ebaf798e {
  background: #ffffff;
}
.comment-popup .popup-header.data-v-ebaf798e {
  position: relative;
  padding: 30rpx;
  border-bottom: 2rpx solid #f1f5f9;
  text-align: center;
}
.comment-popup .popup-header .popup-title.data-v-ebaf798e {
  font-size: 30rpx;
  font-weight: 500;
  color: #606266;
}
.comment-popup .popup-header .popup-close.data-v-ebaf798e {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40rpx;
  color: #909399;
}
.comment-popup .popup-body.data-v-ebaf798e {
  padding: 30rpx;
}
.comment-popup .popup-body .comment-input.data-v-ebaf798e {
  width: 100%;
  min-height: 200rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #303133;
}
.comment-popup .popup-body .popup-footer.data-v-ebaf798e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.comment-popup .popup-body .popup-footer .comment-counter.data-v-ebaf798e {
  font-size: 24rpx;
  color: #909399;
}
.comment-popup .popup-body .popup-footer .comment-submit.data-v-ebaf798e {
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  background: #dcdfe6;
  color: #ffffff;
  font-size: 28rpx;
}
.comment-popup .popup-body .popup-footer .comment-submit.comment-submit-active.data-v-ebaf798e {
  background: #023F98;
}