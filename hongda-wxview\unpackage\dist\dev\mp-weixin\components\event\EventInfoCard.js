"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../utils/config.js");
const utils_date = require("../../utils/date.js");
const utils_location = require("../../utils/location.js");
const _sfc_main = {
  __name: "EventInfoCard",
  props: {
    eventDetail: { type: Object, required: true }
  },
  setup(__props) {
    const props = __props;
    const detailBgUrl = common_vendor.ref("");
    const detailTimeIconUrl = common_vendor.ref("");
    const detailLocationIconUrl = common_vendor.ref("");
    const detailUserIconUrl = common_vendor.ref("");
    common_vendor.onMounted(() => {
      const assets = common_vendor.index.getStorageSync("staticAssets");
      detailBgUrl.value = (assets == null ? void 0 : assets.detail_bg) || "";
      detailTimeIconUrl.value = (assets == null ? void 0 : assets.detail_icon_time) || "";
      detailLocationIconUrl.value = (assets == null ? void 0 : assets.detail_icon_location) || "";
      detailUserIconUrl.value = (assets == null ? void 0 : assets.detail_icon_user) || "";
    });
    const localEvent = common_vendor.computed(() => props.eventDetail || {});
    const calculatedRegistrationStatus = common_vendor.computed(() => {
      var _a, _b;
      const registrationStartTime = (_a = localEvent.value) == null ? void 0 : _a.registrationStartTime;
      const registrationEndTime = (_b = localEvent.value) == null ? void 0 : _b.registrationEndTime;
      try {
        const now = /* @__PURE__ */ new Date();
        if (registrationStartTime && now < new Date(registrationStartTime)) {
          return 0;
        } else if ((!registrationStartTime || now >= new Date(registrationStartTime)) && (!registrationEndTime || now <= new Date(registrationEndTime))) {
          return 1;
        } else if (registrationEndTime && now > new Date(registrationEndTime)) {
          return 2;
        }
        return 1;
      } catch (error) {
        common_vendor.index.__f__("warn", "at components/event/EventInfoCard.vue:88", "报名状态计算失败:", error);
        return 1;
      }
    });
    const formatRegistrationStatus = (status) => {
      switch (status) {
        case 0:
          return "即将开始";
        case 1:
          return "报名中";
        case 2:
          return "报名截止";
        default:
          return "未知";
      }
    };
    const getRegistrationStatusClass = (status) => {
      switch (status) {
        case 0:
          return "not-started";
        case 1:
          return "open";
        case 2:
          return "ended";
        default:
          return "unknown";
      }
    };
    const formatEventTime = common_vendor.computed(() => {
      var _a, _b;
      if (!((_a = localEvent.value) == null ? void 0 : _a.startTime) || !((_b = localEvent.value) == null ? void 0 : _b.endTime)) {
        return "时间待定";
      }
      try {
        const startTime = utils_date.formatDate(localEvent.value.startTime, "YYYY-MM-DD HH:mm");
        const endTime = utils_date.formatDate(localEvent.value.endTime, "YYYY-MM-DD HH:mm");
        return `${startTime} 至 ${endTime}`;
      } catch (error) {
        common_vendor.index.__f__("warn", "at components/event/EventInfoCard.vue:126", "时间格式化失败:", error);
        return "时间格式错误";
      }
    });
    const remainingSpotsNodes = common_vendor.computed(() => {
      if (!localEvent.value) {
        return [{ type: "text", text: "加载中..." }];
      }
      const max = Number(localEvent.value.maxParticipants) || 0;
      if (max === 0) {
        return [{ type: "text", text: "剩余名额: 不限人数" }];
      }
      const registered = Number(localEvent.value.registeredCount) || 0;
      const remaining = Math.max(0, max - registered);
      return [
        {
          type: "node",
          name: "span",
          children: [
            { type: "text", text: "剩余名额: " },
            {
              type: "node",
              name: "span",
              attrs: { style: "color: #023F98;" },
              children: [{ type: "text", text: String(remaining) }]
            },
            { type: "text", text: `/${max}` }
          ]
        }
      ];
    });
    return (_ctx, _cache) => {
      return {
        a: detailBgUrl.value,
        b: common_vendor.t(formatRegistrationStatus(calculatedRegistrationStatus.value)),
        c: common_vendor.n(getRegistrationStatusClass(calculatedRegistrationStatus.value)),
        d: common_vendor.t(localEvent.value.title || ""),
        e: detailTimeIconUrl.value,
        f: common_vendor.t(formatEventTime.value),
        g: detailLocationIconUrl.value,
        h: common_vendor.t(common_vendor.unref(utils_location.formatEventLocation)(localEvent.value)),
        i: detailUserIconUrl.value,
        j: remainingSpotsNodes.value
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-08cb6188"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/event/EventInfoCard.js.map
