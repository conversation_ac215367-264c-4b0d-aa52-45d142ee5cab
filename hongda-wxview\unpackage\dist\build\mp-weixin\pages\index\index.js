"use strict";const e=require("../../common/vendor.js"),o=require("../../api/platform/ad.js");Math||(n+a+t+l+c+s+u+v+r)();const n=()=>"../../components/home/<USER>",a=()=>"../../components/home/<USER>",t=()=>"../../components/home/<USER>",l=()=>"../../components/home/<USER>",s=()=>"../../components/home/<USER>",u=()=>"../../components/home/<USER>",c=()=>"../../components/home/<USER>",r=()=>"../../components/common/PopupAdComponent.js",v=()=>"../../components/layout/CustomTabBar.js",i={__name:"index",setup(n){const a=e.ref(null),t=e.ref(!1),l=e.ref(""),s=e.ref(!1),u=e.ref([]),c=e.ref(0),r=e.ref(null);let v=!1;const i=()=>{a.value&&a.value.loadMore&&a.value.loadMore()},m=e=>{t.value=!!e},p=()=>{c.value<u.value.length?(r.value=u.value[c.value],s.value=!0,console.log(`显示第 ${c.value+1} 个广告:`,r.value.title)):(console.log("所有广告已显示完毕"),s.value=!1,r.value=null)},d=()=>{if(s.value=!1,c.value++,c.value>=u.value.length)return v=!0,void console.log("所有广告显示完毕，已标记本次会话为已显示");setTimeout((()=>{p()}),300)},g=()=>{e.index.navigateTo({url:"/pages_sub/pages_profile/contact"})};return e.onLoad((()=>{console.log("首页 onLoad - 页面首次加载")})),e.onShow((()=>{console.log("首页 onShow - 页面显示",{hasShownInCurrentSession:v}),e.index.hideTabBar(),(async()=>{try{if(v)return void console.log("本次会话已经显示过弹屏广告，不再显示");const e=await o.getAdListByPositionApi("SPLASH_SCREEN",{pageSize:10});console.log("弹窗广告API返回结果:",e),e&&e.data&&e.data.length>0?(u.value=e.data,c.value=0,console.log(`获取到 ${u.value.length} 个广告，开始显示第一个`),p()):(console.log("没有广告数据可显示"),v=!0)}catch(e){console.error("获取弹窗广告失败:",e.message||e),v=!0}})();const n=e.index.getStorageSync("staticAssets");l.value=(null==n?void 0:n.fab_customer_service_icon)||""})),(o,n)=>e.e({a:e.sr(a,"36f70346-6",{k:"activityRef"}),b:e.o(m),c:t.value},(t.value,{}),{d:e.o(i),e:e.p({current:0}),f:l.value,g:e.o(g),h:s.value&&r.value},s.value&&r.value?{i:e.o(d),j:e.p({show:s.value,"ad-data":r.value})}:{})}},m=e._export_sfc(i,[["__scopeId","data-v-36f70346"]]);wx.createPage(m);
