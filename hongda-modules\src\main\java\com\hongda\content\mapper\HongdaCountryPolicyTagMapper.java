package com.hongda.content.mapper;

import com.hongda.content.domain.HongdaCountryPolicyTag;

import java.util.List;

/**
 * 政策标签管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface HongdaCountryPolicyTagMapper
{
    /**
     * 查询政策标签管理
     *
     * @param tagId 政策标签管理主键
     * @return 政策标签管理
     */
    public HongdaCountryPolicyTag selectHongdaCountryPolicyTagByTagId(Long tagId);

    /**
     * 查询政策标签管理列表
     *
     * @param hongdaCountryPolicyTag 政策标签管理
     * @return 政策标签管理集合
     */
    public List<HongdaCountryPolicyTag> selectHongdaCountryPolicyTagList(HongdaCountryPolicyTag hongdaCountryPolicyTag);

    /**
     * 新增政策标签管理
     *
     * @param hongdaCountryPolicyTag 政策标签管理
     * @return 结果
     */
    public int insertHongdaCountryPolicyTag(HongdaCountryPolicyTag hongdaCountryPolicyTag);

    /**
     * 修改政策标签管理
     *
     * @param hongdaCountryPolicyTag 政策标签管理
     * @return 结果
     */
    public int updateHongdaCountryPolicyTag(HongdaCountryPolicyTag hongdaCountryPolicyTag);

    /**
     * 删除政策标签管理
     *
     * @param tagId 政策标签管理主键
     * @return 结果
     */
    public int deleteHongdaCountryPolicyTagByTagId(Long tagId);

    /**
     * 批量删除政策标签管理
     *
     * @param tagIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaCountryPolicyTagByTagIds(Long[] tagIds);

    /**
     * 根据文章ID查询其关联的标签列表
     *
     * @param articleId 文章ID
     * @return 标签列表
     */
    public List<HongdaCountryPolicyTag> selectTagsByArticleId(Long articleId);

    // ...已有的代码...

    /**
     * [新增] 根据标签ID统计关联的文章数量
     *
     * @param tagId 标签ID
     * @return 关联的文章数量
     */
    public int countArticleRelationsByTagId(Long tagId);
}
