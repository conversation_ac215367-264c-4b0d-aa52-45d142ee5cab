import request from '@/utils/request'

// 查询政策标签管理列表
export function listPolicytag(query) {
  return request({
    url: '/content/policytag/list',
    method: 'get',
    params: query
  })
}

// 查询政策标签管理详细
export function getPolicytag(tagId) {
  return request({
    url: '/content/policytag/' + tagId,
    method: 'get'
  })
}

// 新增政策标签管理
export function addPolicytag(data) {
  return request({
    url: '/content/policytag',
    method: 'post',
    data: data
  })
}

// 修改政策标签管理
export function updatePolicytag(data) {
  return request({
    url: '/content/policytag',
    method: 'put',
    data: data
  })
}

// 删除政策标签管理
export function delPolicytag(tagId) {
  return request({
    url: '/content/policytag/' + tagId,
    method: 'delete'
  })
}
