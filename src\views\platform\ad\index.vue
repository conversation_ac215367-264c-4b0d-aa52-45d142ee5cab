<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="导航标题" prop="title">
        <el-input
            v-model="queryParams.title"
            placeholder="请输入导航标题"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
          <el-option
              v-for="dict in hongda_common_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['platform:nav:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['platform:nav:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['platform:nav:remove']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="navList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="导航ID" align="center" prop="id" width="80"/>
      <el-table-column label="导航标题" align="center" prop="title"/>
      <el-table-column label="图标" align="center">
        <template #default="scope">
          <image-preview :src="scope.row.iconUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>

      <el-table-column label="关联目标" align="center" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-if="scope.row.linkType && scope.row.linkTarget">
            <el-tag>{{ linkTypeMap[scope.row.linkType] || '未知类型' }}</el-tag>
            <span style="margin-left: 5px;">{{ scope.row.linkTargetTitle || scope.row.linkTarget }}</span>
          </div>
          <span v-else>无</span>
        </template>
      </el-table-column>

      <el-table-column label="排序" align="center" prop="sortOrder" width="100"/>
      <el-table-column label="状态" align="center" prop="status" width="120">
        <template #default="scope">
          <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
              v-hasPermi="['platform:nav:edit']"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['platform:nav:edit']">修改
          </el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['platform:nav:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="700px" append-to-body :close-on-click-modal="false">
      <el-form ref="navRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="导航标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入导航标题"/>
        </el-form-item>
        <el-form-item label="图标" prop="iconUrl">
          <image-upload v-model="form.iconUrl" :limit="1"/>
        </el-form-item>

        <el-divider content-position="center">链接配置</el-divider>

        <el-form-item label="链接类型" prop="linkType">
          <el-select v-model="form.linkType" placeholder="请选择链接类型" @change="onLinkTypeChange" style="width: 100%;">
            <el-option v-for="item in linkTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <div v-if="form.linkType">
          <el-form-item label="链接目标" prop="linkTarget">
            <el-select v-if="form.linkType === 'ARTICLE'" v-model="form.linkTarget" placeholder="点击选择或输入资讯标题搜索" style="width: 100%;"
                       filterable remote :remote-method="searchArticles" @focus="handleArticleFocus" :loading="articleLoading">
              <el-option v-for="item in articleOptions" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>

            <el-select v-else-if="form.linkType === 'EVENT'" v-model="form.linkTarget" placeholder="点击选择或输入活动标题搜索" style="width: 100%;"
                       filterable remote :remote-method="searchEvents" @focus="handleEventFocus" :loading="eventLoading">
              <el-option v-for="item in eventOptions" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>

            <el-select v-else-if="form.linkType === 'COUNTRY'" v-model="form.linkTarget" placeholder="点击选择或输入国别名称搜索" style="width: 100%;"
                       filterable remote :remote-method="searchCountries" @focus="handleCountryFocus" :loading="countryLoading">
              <el-option v-for="item in countryOptions" :key="item.id" :label="item.nameCn" :value="item.id" />
            </el-select>

            <el-select v-else-if="form.linkType === 'PARK'" v-model="form.linkTarget" placeholder="点击选择或输入园区名称搜索" style="width: 100%;"
                       filterable remote :remote-method="searchParks" @focus="handleParkFocus" :loading="parkLoading">
              <el-option v-for="item in parkOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>

            <el-select v-else-if="form.linkType === 'CUSTOM_PAGE'" v-model="form.linkTarget" placeholder="点击选择或输入自定义页面标题搜索" style="width: 100%;"
                       filterable remote :remote-method="searchCustomPages" @focus="handleCustomPageFocus" :loading="customPageLoading">
              <el-option v-for="item in customPageOptions" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>

            <el-select v-else-if="form.linkType === 'PAGE'" v-model="form.linkTarget" placeholder="请选择内部页面" style="width: 100%;" filterable>
              <el-option v-for="item in pageOptions" :key="item.id" :label="`${item.title} (${item.targetUrl})`" :value="item.targetUrl" />
            </el-select>

            <el-input v-else-if="form.linkType === 'EXTERNAL'" v-model="form.linkTarget" type="textarea" placeholder="请输入以 https:// 或 http:// 开头的完整链接" />

            <el-input v-else-if="form.linkType === 'NONE'" value="不设置任何跳转" disabled />
          </el-form-item>
        </div>

        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Nav">
import { addNav, delNav, getNav, listNav, updateNav } from "@/api/platform/nav";
import { listPage, getPage } from "@/api/platform/page";
import { listArticle, getArticle } from "@/api/content/article";
import { listEvent, getEvent } from "@/api/content/event";
import { listCountry, getCountry } from "@/api/content/country";
import { listPark, getPark } from "@/api/content/park";

const { proxy } = getCurrentInstance();
const { hongda_common_status } = proxy.useDict('hongda_common_status');

const navList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const customPageOptions = ref([]);
const pageOptions = ref([]);
const articleOptions = ref([]);
const eventOptions = ref([]);
const countryOptions = ref([]);
const parkOptions = ref([]);

const customPageLoading = ref(false);
const articleLoading = ref(false);
const eventLoading = ref(false);
const countryLoading = ref(false);
const parkLoading = ref(false);

const linkTypeOptions = ref([
  { value: 'NONE', label: '无链接 (仅展示)' },
  { value: 'EXTERNAL', label: '外部链接 (H5网页)' },
  { value: 'PAGE', label: '内部页面 (通用)' },
  { value: 'CUSTOM_PAGE', label: '指定自定义页面' },
  { value: 'ARTICLE', label: '指定资讯' },
  { value: 'EVENT', label: '指定活动' },
  { value: 'COUNTRY', label: '指定国别' },
  { value: 'PARK', label: '指定园区' }
]);

const linkTypeMap = {
  NONE: '无链接',
  EXTERNAL: '外部链接',
  PAGE: '内部页面',
  CUSTOM_PAGE: '自定义页面',
  ARTICLE: '指定资讯',
  EVENT: '指定活动',
  COUNTRY: '指定国别',
  PARK: '指定园区'
};


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    positionCode: "HOME_QUICK_NAV",
    status: null,
  },
  rules: {
    title: [{required: true, message: "导航标题不能为空", trigger: "blur"}],
    iconUrl: [{required: true, message: "图标不能为空", trigger: "blur"}],
    sortOrder: [{required: true, message: "排序不能为空", trigger: "blur"}],
    linkType: [ { required: true, message: "链接类型不能为空", trigger: "change" } ],
    linkTarget: [{
      required: true,
      validator: (rule, value, callback) => {
        if (data.form.linkType !== 'NONE' && !value) {
          callback(new Error("链接目标不能为空"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const searchArticles = (query) => {
  articleLoading.value = true;
  listArticle({ title: query, pageSize: 50 }).then(response => {
    articleOptions.value = response.rows;
    articleLoading.value = false;
  });
};
const searchEvents = (query) => {
  eventLoading.value = true;
  listEvent({ title: query, pageSize: 50 }).then(response => {
    eventOptions.value = response.rows;
    eventLoading.value = false;
  });
};
const searchCountries = (query) => {
  countryLoading.value = true;
  listCountry({ nameCn: query, pageSize: 50 }).then(response => {
    countryOptions.value = response.rows;
    countryLoading.value = false;
  });
};
const searchParks = (query) => {
  parkLoading.value = true;
  listPark({ name: query, pageSize: 50 }).then(response => {
    parkOptions.value = response.rows;
    parkLoading.value = false;
  });
};
const searchCustomPages = (query) => {
  customPageLoading.value = true;
  listPage({ title: query, pageType: 'RICH_TEXT', pageSize: 50 }).then(response => {
    customPageOptions.value = response.rows;
    customPageLoading.value = false;
  });
};

const handleArticleFocus = () => {
  if (articleOptions.value.length === 0) {
    searchArticles('');
  }
};
const handleEventFocus = () => {
  if (eventOptions.value.length === 0) {
    searchEvents('');
  }
};
const handleCountryFocus = () => {
  if (countryOptions.value.length === 0) {
    searchCountries('');
  }
};
const handleParkFocus = () => {
  if (parkOptions.value.length === 0) {
    searchParks('');
  }
};
const handleCustomPageFocus = () => {
  if (customPageOptions.value.length === 0) {
    searchCustomPages('');
  }
};

function onLinkTypeChange() {
  form.value.linkTarget = null;
  customPageOptions.value = [];
  articleOptions.value = [];
  eventOptions.value = [];
  countryOptions.value = [];
  parkOptions.value = [];
}

function getPageList() {
  listPage({ pageNum: 1, pageSize: 9999 }).then(response => {
    pageOptions.value = response.rows.filter(p => p.pageType === 'INTERNAL_LINK' || p.pageType === 'EXTERNAL_LINK');
  });
}

function getList() {
  loading.value = true;
  listNav(queryParams.value).then(response => {
    navList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function handleStatusChange(row) {
  let text = row.status === 1 ? "启用" : "禁用";
  proxy.$modal.confirm('确认要"' + text + '"导航"' + row.title + '"吗？').then(function () {
    return updateNav({id: row.id, status: row.status});
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function () {
    row.status = row.status === 0 ? 1 : 0;
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    id: null,
    title: null,
    iconUrl: null,
    linkType: 'NONE',
    linkTarget: null,
    positionCode: "HOME_QUICK_NAV",
    sortOrder: 0,
    status: 1,
  };
  proxy.resetForm("navRef");
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.positionCode = "HOME_QUICK_NAV";
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  getPageList();
  open.value = true;
  title.value = "添加导航配置";
}

function handleUpdate(row) {
  reset();
  getPageList();
  const _id = row.id || ids.value[0];

  getNav(_id).then(response => {
    form.value = response.data;

    const idBasedLinkTypes = ['CUSTOM_PAGE', 'ARTICLE', 'EVENT', 'COUNTRY', 'PARK'];
    if (idBasedLinkTypes.includes(form.value.linkType) && form.value.linkTarget) {
      form.value.linkTarget = parseInt(form.value.linkTarget, 10);
    }

    const targetId = form.value.linkTarget;
    if (targetId && form.value.linkType) {
      switch (form.value.linkType) {
        case 'CUSTOM_PAGE':
          getPage(targetId).then(res => { if(res.data) customPageOptions.value = [res.data]; });
          break;
        case 'ARTICLE':
          getArticle(targetId).then(res => { if(res.data) articleOptions.value = [res.data]; });
          break;
        case 'EVENT':
          getEvent(targetId).then(res => { if(res.data) eventOptions.value = [res.data]; });
          break;
        case 'COUNTRY':
          getCountry(targetId).then(res => { if(res.data) countryOptions.value = [res.data]; });
          break;
        case 'PARK':
          getPark(targetId).then(res => { if(res.data) parkOptions.value = [res.data]; });
          break;
      }
    }

    open.value = true;
    title.value = "修改导航配置";
  });
}

function submitForm() {
  proxy.$refs["navRef"].validate(valid => {
    if (valid) {
      form.value.positionCode = "HOME_QUICK_NAV";

      if(form.value.linkType === 'NONE') {
        form.value.linkTarget = '#';
      }

      const apiCall = form.value.id != null ? updateNav(form.value) : addNav(form.value);
      apiCall.then(() => {
        proxy.$modal.msgSuccess(form.value.id != null ? "修改成功" : "新增成功");
        open.value = false;
        getList();
      });
    }
  });
}

function handleDelete(row) {
  const _ids = row.id ? [row.id] : ids.value;
  proxy.$modal.confirm('是否确认删除导航配置编号为"' + _ids + '"的数据项？').then(function () {
    return delNav(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

getList();
</script>

<style scoped>
.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.5;
}
</style>