"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_event = require("../../api/data/event.js");
const utils_tools = require("../../utils/tools.js");
const utils_date = require("../../utils/date.js");
const utils_config = require("../../utils/config.js");
if (!Array) {
  const _easycom_up_subsection2 = common_vendor.resolveComponent("up-subsection");
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_loadmore2 = common_vendor.resolveComponent("up-loadmore");
  (_easycom_up_subsection2 + _easycom_up_icon2 + _easycom_up_empty2 + _easycom_up_button2 + _easycom_up_loadmore2)();
}
const _easycom_up_subsection = () => "../../uni_modules/uview-plus/components/u-subsection/u-subsection.js";
const _easycom_up_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
const _easycom_up_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_loadmore = () => "../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js";
if (!Math) {
  (_easycom_up_subsection + CustomSearchBox + _easycom_up_icon + _easycom_up_empty + _easycom_up_button + EventCard + _easycom_up_loadmore + EventCalendarTimeline + CustomTabBar)();
}
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const CustomSearchBox = () => "../../components/home/<USER>";
const EventCard = () => "../../components/event/EventCard.js";
const EventCalendarTimeline = () => "../../components/event/EventCalendarTimeline.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const searchKeyword = common_vendor.ref("");
    const eventList = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const showRetry = common_vendor.ref(false);
    const eventBgUrl = common_vendor.ref("");
    const pagination = common_vendor.ref({
      pageNum: 1,
      pageSize: utils_config.PAGE_CONFIG.DEFAULT_PAGE_SIZE,
      total: 0,
      hasMore: true
    });
    const appliedFiltersList = common_vendor.ref({
      sortBy: 1,
      location: 1,
      timeRange: 1,
      status: 1
    });
    const appliedFiltersCalendar = common_vendor.ref({
      location: 1,
      timeRange: 1
    });
    const tempFiltersList = common_vendor.ref({
      sortBy: 1,
      location: 1,
      timeRange: 1,
      status: 1
    });
    const tempFiltersCalendar = common_vendor.ref({
      location: 1,
      timeRange: 1
    });
    const showSortPanel = common_vendor.ref(false);
    const showLocationPanel = common_vendor.ref(false);
    const showTimePanel = common_vendor.ref(false);
    const showStatusPanel = common_vendor.ref(false);
    const calendarNotchLeft = common_vendor.ref("60rpx");
    const options1 = common_vendor.ref([
      {
        label: "综合排序",
        value: 1
      },
      {
        label: "最新发布",
        value: 2
      },
      {
        label: "最近开始",
        value: 3
      }
    ]);
    const allRegionOption = common_vendor.ref({ label: "全部地区", value: 1 });
    const hotCities = common_vendor.ref([
      { label: "北京", value: 2 },
      { label: "上海", value: 3 },
      { label: "广州", value: 4 },
      { label: "深圳", value: 5 }
    ]);
    const otherCities = common_vendor.ref([]);
    const options2 = common_vendor.computed(() => [
      allRegionOption.value,
      ...hotCities.value,
      ...otherCities.value.map((city, index) => ({
        label: city,
        value: 100 + index
        // 避免与热门城市ID冲突
      }))
    ]);
    const options3 = common_vendor.ref([
      {
        label: "全部时间",
        value: 1
      },
      {
        label: "1周内",
        value: 2
      },
      {
        label: "1月内",
        value: 3
      },
      {
        label: "1年内",
        value: 4
      }
    ]);
    const options4 = common_vendor.ref([
      {
        label: "全部状态",
        value: 1
      },
      {
        label: "即将开始",
        value: 2
      },
      {
        label: "报名中",
        value: 3
      },
      {
        label: "报名截止",
        value: 4
      }
    ]);
    const loadMoreStatus = common_vendor.computed(() => {
      if (isLoading.value)
        return "loading";
      if (!pagination.value.hasMore)
        return "nomore";
      return "more";
    });
    const groupedEvents = common_vendor.computed(() => {
      if (!eventList.value || eventList.value.length === 0) {
        return [];
      }
      const groups = /* @__PURE__ */ new Map();
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      eventList.value.forEach((event) => {
        const eventDate = utils_date.parseDate(event.startTime);
        const year = eventDate.getFullYear();
        const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
        const month = String(eventDate.getMonth() + 1).padStart(2, "0");
        const day = String(eventDate.getDate()).padStart(2, "0");
        let displayDate;
        if (year !== currentYear) {
          displayDate = `${year}年${month}月${day}日`;
        } else {
          displayDate = `${month}.${day}`;
        }
        const monthLocal = String(eventDate.getMonth() + 1).padStart(2, "0");
        const dayLocal = String(eventDate.getDate()).padStart(2, "0");
        const dateKey = `${eventDate.getFullYear()}-${monthLocal}-${dayLocal}`;
        if (!groups.has(dateKey)) {
          groups.set(dateKey, {
            date: dateKey,
            formattedDate: displayDate,
            // 使用新的日期格式
            dayOfWeek: weekdays[eventDate.getDay()],
            events: []
          });
        }
        groups.get(dateKey).events.push(event);
      });
      return Array.from(groups.values());
    });
    const limitedCalendarEvents = common_vendor.computed(() => {
      let totalEvents = 0;
      const limitedGroups = [];
      for (const group of groupedEvents.value) {
        if (totalEvents >= 10)
          break;
        const remainingSlots = 10 - totalEvents;
        const eventsToShow = group.events.slice(0, remainingSlots);
        limitedGroups.push({
          ...group,
          events: eventsToShow
        });
        totalEvents += eventsToShow.length;
      }
      return limitedGroups;
    });
    const hasMoreCalendarEvents = common_vendor.computed(() => {
      const totalEvents = groupedEvents.value.reduce((sum, group) => sum + group.events.length, 0);
      return totalEvents > 10;
    });
    const fetchEventCities = async () => {
      try {
        const response = await api_data_event.getEventCitiesApi();
        let cityList = null;
        if (Array.isArray(response)) {
          cityList = response;
        } else if (response && Array.isArray(response.data)) {
          cityList = response.data;
        } else if (response && response.code === 200 && Array.isArray(response.data)) {
          cityList = response.data;
        }
        common_vendor.index.__f__("log", "at pages/event/index.vue:608", "提取的城市列表:", cityList);
        if (cityList && Array.isArray(cityList)) {
          const hotCityNames = hotCities.value.map((city) => city.label);
          const filteredCities = cityList.filter(
            (city) => city && city.trim() && !hotCityNames.includes(city.trim())
          );
          otherCities.value = filteredCities;
        } else {
          common_vendor.index.showToast({
            title: "城市数据格式错误",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/event/index.vue:626", "获取城市列表失败:", error);
        common_vendor.index.showToast({
          title: "获取城市列表失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const buildQueryParams = (isLoadMore = false) => {
      const params = {
        pageNum: isLoadMore ? pagination.value.pageNum : 1,
        pageSize: pagination.value.pageSize
      };
      if (searchKeyword.value.trim()) {
        params.title = searchKeyword.value.trim();
      }
      const filters = currentTab.value === 1 ? appliedFiltersCalendar.value : appliedFiltersList.value;
      if (filters.location > 1) {
        const hotLocationMap = {
          2: "北京",
          3: "上海",
          4: "广州",
          5: "深圳"
        };
        if (hotLocationMap[filters.location]) {
          params.location = hotLocationMap[filters.location];
        } else if (filters.location >= 100) {
          const otherIndex = filters.location - 100;
          if (otherIndex < otherCities.value.length) {
            params.location = otherCities.value[otherIndex];
          }
        }
      }
      if (currentTab.value === 0 && filters.status > 1) {
        const registrationStatusMap = {
          2: 0,
          // 即将开始 -> registrationStatus: 0
          3: 1,
          // 报名中 -> registrationStatus: 1
          4: 2
          // 报名截止 -> registrationStatus: 2
        };
        if (registrationStatusMap.hasOwnProperty(filters.status)) {
          params.registrationStatus = registrationStatusMap[filters.status];
        } else {
          common_vendor.index.__f__("warn", "at pages/event/index.vue:750", "未知的报名状态筛选值:", filters.status);
        }
      }
      if (currentTab.value === 1) {
        params.orderBy = "startTime";
        params.isAsc = "asc";
      } else {
        switch (filters.sortBy) {
          case 1:
            params.orderBy = "comprehensive";
            break;
          case 2:
            params.orderBy = "startTime";
            params.isAsc = "asc";
            common_vendor.index.__f__("log", "at pages/event/index.vue:768", "按时间排序: 最近开始优先");
            break;
          case 3:
            params.orderBy = "createTime";
            params.isAsc = "desc";
            common_vendor.index.__f__("log", "at pages/event/index.vue:773", "按最新发布排序: 最新创建的活动优先");
            break;
          default:
            params.orderBy = "createTime";
            params.isAsc = "desc";
            common_vendor.index.__f__("log", "at pages/event/index.vue:778", "默认排序: 最新发布");
        }
      }
      if (filters.timeRange > 1) {
        const timeRange = buildTimeRangeParams(filters.timeRange);
        if (timeRange) {
          Object.assign(params, timeRange);
        }
      }
      return params;
    };
    const buildTimeRangeParams = (timeRangeValue) => {
      const now = /* @__PURE__ */ new Date();
      let startTime = null;
      let endTime = null;
      switch (timeRangeValue) {
        case 2:
          startTime = now;
          endTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1e3);
          common_vendor.index.__f__("log", "at pages/event/index.vue:807", "时间筛选: 1周内 -", startTime.toLocaleDateString(), "到", endTime.toLocaleDateString());
          break;
        case 3:
          startTime = now;
          endTime = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
          common_vendor.index.__f__("log", "at pages/event/index.vue:812", "时间筛选: 1月内 -", startTime.toLocaleDateString(), "到", endTime.toLocaleDateString());
          break;
        case 4:
          startTime = now;
          endTime = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
          common_vendor.index.__f__("log", "at pages/event/index.vue:817", "时间筛选: 1年内 -", startTime.toLocaleDateString(), "到", endTime.toLocaleDateString());
          break;
        default:
          common_vendor.index.__f__("log", "at pages/event/index.vue:820", "时间筛选: 全部时间");
          return null;
      }
      return {
        timeRangeStart: startTime.toISOString(),
        timeRangeEnd: endTime.toISOString()
      };
    };
    const fetchEventList = async (isLoadMore = false) => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      try {
        const params = buildQueryParams(isLoadMore);
        common_vendor.index.__f__("log", "at pages/event/index.vue:840", "请求参数:", params);
        let response;
        if (currentTab.value === 1) {
          response = await api_data_event.getCalendarEventsApi(params);
        } else {
          response = await api_data_event.getEventListApi(params);
        }
        const {
          rows = [],
          total = 0
        } = response;
        if (isLoadMore) {
          eventList.value.push(...rows);
        } else {
          eventList.value = rows;
          pagination.value.pageNum = 1;
        }
        pagination.value.total = total;
        pagination.value.hasMore = eventList.value.length < total;
        showRetry.value = false;
        common_vendor.index.__f__("log", "at pages/event/index.vue:868", `获取活动列表成功: ${rows.length} 条记录, 总计: ${total}`);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/event/index.vue:871", "获取活动列表失败:", error);
        let errorMessage = "获取活动列表失败";
        if (error.message && error.message.includes("timeout")) {
          errorMessage = "网络请求超时，请重试";
        } else if (error.message && error.message.includes("Network")) {
          errorMessage = "网络连接失败，请检查网络";
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
        if (!isLoadMore && eventList.value.length === 0) {
          showRetry.value = true;
        }
      } finally {
        isLoading.value = false;
        isRefreshing.value = false;
      }
    };
    const debouncedSearch = utils_tools.debounce(() => {
      fetchEventList();
    }, 500);
    const getCurrentSortTitleList = common_vendor.computed(() => {
      const currentOption = options1.value.find((option) => option.value === appliedFiltersList.value.sortBy);
      const title = currentOption ? currentOption.label : "综合排序";
      return title;
    });
    const getCurrentStatusTitleList = common_vendor.computed(() => {
      const currentOption = options4.value.find((option) => option.value === appliedFiltersList.value.status);
      const title = currentOption ? currentOption.label : "全部状态";
      return title;
    });
    const getCurrentLocationTitleList = common_vendor.computed(() => {
      const currentOption = options2.value.find((option) => option.value === appliedFiltersList.value.location);
      return currentOption ? currentOption.label : "热门地区";
    });
    const getCurrentTimeTitleList = common_vendor.computed(() => {
      const currentOption = options3.value.find((option) => option.value === appliedFiltersList.value.timeRange);
      return currentOption ? currentOption.label : "全部时间";
    });
    const getCurrentLocationTitleCalendar = common_vendor.computed(() => {
      const currentOption = options2.value.find((option) => option.value === appliedFiltersCalendar.value.location);
      return currentOption ? currentOption.label : "热门地区";
    });
    const getCurrentTimeTitleCalendar = common_vendor.computed(() => {
      const currentOption = options3.value.find((option) => option.value === appliedFiltersCalendar.value.timeRange);
      return currentOption ? currentOption.label : "全部时间";
    });
    const tabChange = (index) => {
      currentTab.value = index;
      eventList.value = [];
      pagination.value.pageNum = 1;
      pagination.value.hasMore = true;
      fetchEventList();
    };
    const onSearch = (value) => {
      searchKeyword.value = value;
      debouncedSearch();
    };
    const onFilterChange = () => {
      common_vendor.index.__f__("log", "at pages/event/index.vue:981", "筛选条件变更，重置数据并重新加载");
      eventList.value = [];
      pagination.value.pageNum = 1;
      pagination.value.hasMore = true;
      fetchEventList();
    };
    const toggleSortPanel = () => {
      showSortPanel.value = !showSortPanel.value;
      showLocationPanel.value = false;
      showTimePanel.value = false;
      showStatusPanel.value = false;
    };
    const toggleLocationPanel = () => {
      showLocationPanel.value = !showLocationPanel.value;
      showSortPanel.value = false;
      showTimePanel.value = false;
      showStatusPanel.value = false;
      calendarNotchLeft.value = "60rpx";
    };
    const toggleTimePanel = () => {
      showTimePanel.value = !showTimePanel.value;
      showSortPanel.value = false;
      showLocationPanel.value = false;
      showStatusPanel.value = false;
      calendarNotchLeft.value = "240rpx";
    };
    const toggleStatusPanel = () => {
      showStatusPanel.value = !showStatusPanel.value;
      showSortPanel.value = false;
      showLocationPanel.value = false;
      showTimePanel.value = false;
    };
    const selectSortOption = (value) => {
      tempFiltersList.value.sortBy = value;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1045", "临时选择排序:", value);
    };
    const selectLocationOption = (value) => {
      if (currentTab.value === 1) {
        tempFiltersCalendar.value.location = value;
      } else {
        tempFiltersList.value.location = value;
      }
      common_vendor.index.__f__("log", "at pages/event/index.vue:1058", "临时选择地区:", value);
    };
    const selectTimeOption = (value) => {
      if (currentTab.value === 1) {
        tempFiltersCalendar.value.timeRange = value;
      } else {
        tempFiltersList.value.timeRange = value;
      }
      common_vendor.index.__f__("log", "at pages/event/index.vue:1071", "临时选择时间:", value);
    };
    const selectStatusOption = (value) => {
      tempFiltersList.value.status = value;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1081", "临时选择状态:", value);
    };
    const resetSortFilter = () => {
      tempFiltersList.value.sortBy = 1;
      appliedFiltersList.value.sortBy = 1;
      showSortPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1094", "重置排序筛选为初始状态");
      onFilterChange();
    };
    const completeSortFilter = () => {
      appliedFiltersList.value.sortBy = tempFiltersList.value.sortBy;
      showSortPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1105", "应用排序筛选:", tempFiltersList.value.sortBy);
      onFilterChange();
    };
    const resetLocationFilter = () => {
      if (currentTab.value === 1) {
        tempFiltersCalendar.value.location = 1;
        appliedFiltersCalendar.value.location = 1;
      } else {
        tempFiltersList.value.location = 1;
        appliedFiltersList.value.location = 1;
      }
      showLocationPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1121", "重置地区筛选为初始状态");
      onFilterChange();
    };
    const completeLocationFilter = () => {
      if (currentTab.value === 1) {
        appliedFiltersCalendar.value.location = tempFiltersCalendar.value.location;
      } else {
        appliedFiltersList.value.location = tempFiltersList.value.location;
      }
      showLocationPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1135", "应用地区筛选:", currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location);
      onFilterChange();
      calendarNotchLeft.value = "60rpx";
    };
    const resetTimeFilter = () => {
      if (currentTab.value === 1) {
        tempFiltersCalendar.value.timeRange = 1;
        appliedFiltersCalendar.value.timeRange = 1;
      } else {
        tempFiltersList.value.timeRange = 1;
        appliedFiltersList.value.timeRange = 1;
      }
      showTimePanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1153", "重置时间筛选为初始状态");
      onFilterChange();
    };
    const completeTimeFilter = () => {
      if (currentTab.value === 1) {
        appliedFiltersCalendar.value.timeRange = tempFiltersCalendar.value.timeRange;
      } else {
        appliedFiltersList.value.timeRange = tempFiltersList.value.timeRange;
      }
      showTimePanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1167", "应用时间筛选:", currentTab.value === 1 ? tempFiltersCalendar.value.timeRange : tempFiltersList.value.timeRange);
      onFilterChange();
      calendarNotchLeft.value = "240rpx";
    };
    const resetStatusFilter = () => {
      tempFiltersList.value.status = 1;
      appliedFiltersList.value.status = 1;
      showStatusPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1181", "重置状态筛选为初始状态");
      onFilterChange();
    };
    const completeStatusFilter = () => {
      appliedFiltersList.value.status = tempFiltersList.value.status;
      showStatusPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1192", "应用状态筛选:", tempFiltersList.value.status);
      onFilterChange();
    };
    const goToDetail = (event) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_event/detail?id=${event.id}`
      });
    };
    const switchToListView = () => {
      currentTab.value = 0;
      fetchEventList();
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      pagination.value.pageNum = 1;
      fetchEventList();
    };
    const onLoadMore = () => {
      if (!pagination.value.hasMore || isLoading.value)
        return;
      pagination.value.pageNum++;
      fetchEventList(true);
    };
    common_vendor.onLoad(() => {
      const assets = common_vendor.index.getStorageSync("staticAssets");
      eventBgUrl.value = (assets == null ? void 0 : assets.eventbg) || "";
      fetchEventCities();
      fetchEventList();
      common_vendor.index.$on("dataChanged", () => {
        common_vendor.index.__f__("log", "at pages/event/index.vue:1246", "活动列表页收到数据变化事件，刷新列表...");
        fetchEventList();
        common_vendor.index.showToast({
          title: "列表已更新",
          icon: "success",
          duration: 1500
        });
      });
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("dataChanged");
    });
    common_vendor.onReachBottom(() => {
      onLoadMore();
    });
    common_vendor.onPullDownRefresh(() => {
      onRefresh();
      setTimeout(() => {
        common_vendor.index.stopPullDownRefresh();
      }, 1e3);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: eventBgUrl.value,
        b: common_vendor.o(tabChange),
        c: common_vendor.p({
          list: ["列表", "日历"],
          current: currentTab.value,
          mode: "subsection",
          activeColor: "#f56c6c"
        }),
        d: common_vendor.o(onSearch),
        e: common_vendor.o(common_vendor.unref(debouncedSearch)),
        f: common_vendor.o(($event) => searchKeyword.value = $event),
        g: common_vendor.p({
          placeholder: "搜索活动",
          modelValue: searchKeyword.value
        }),
        h: currentTab.value === 0
      }, currentTab.value === 0 ? common_vendor.e({
        i: common_vendor.t(getCurrentSortTitleList.value),
        j: showSortPanel.value ? 1 : "",
        k: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        l: common_vendor.o(toggleSortPanel),
        m: common_vendor.t(getCurrentLocationTitleList.value),
        n: showLocationPanel.value ? 1 : "",
        o: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        p: common_vendor.o(toggleLocationPanel),
        q: common_vendor.t(getCurrentTimeTitleList.value),
        r: showTimePanel.value ? 1 : "",
        s: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        t: common_vendor.o(toggleTimePanel),
        v: common_vendor.t(getCurrentStatusTitleList.value),
        w: showStatusPanel.value ? 1 : "",
        x: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        y: common_vendor.o(toggleStatusPanel),
        z: showSortPanel.value
      }, showSortPanel.value ? {
        A: common_vendor.f(options1.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": tempFiltersList.value.sortBy === option.value
            }),
            d: common_vendor.o(($event) => selectSortOption(option.value), option.value)
          };
        }),
        B: common_vendor.o(resetSortFilter),
        C: common_vendor.o(completeSortFilter)
      } : {}, {
        D: showLocationPanel.value
      }, showLocationPanel.value ? common_vendor.e({
        E: common_vendor.t(allRegionOption.value.label),
        F: common_vendor.n({
          "active": (currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location) === allRegionOption.value.value
        }),
        G: common_vendor.o(($event) => selectLocationOption(allRegionOption.value.value)),
        H: common_vendor.f(hotCities.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location) === option.value
            }),
            d: common_vendor.o(($event) => selectLocationOption(option.value), option.value)
          };
        }),
        I: otherCities.value.length > 0
      }, otherCities.value.length > 0 ? {
        J: common_vendor.f(otherCities.value, (city, index, i0) => {
          return {
            a: common_vendor.t(city),
            b: 100 + index,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location) === 100 + index
            }),
            d: common_vendor.o(($event) => selectLocationOption(100 + index), 100 + index)
          };
        })
      } : {}, {
        K: common_vendor.o(resetLocationFilter),
        L: common_vendor.o(completeLocationFilter)
      }) : {}, {
        M: showTimePanel.value
      }, showTimePanel.value ? {
        N: common_vendor.f(options3.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.timeRange : tempFiltersList.value.timeRange) === option.value
            }),
            d: common_vendor.o(($event) => selectTimeOption(option.value), option.value)
          };
        }),
        O: common_vendor.o(resetTimeFilter),
        P: common_vendor.o(completeTimeFilter)
      } : {}, {
        Q: showStatusPanel.value
      }, showStatusPanel.value ? {
        R: common_vendor.f(options4.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": tempFiltersList.value.status === option.value
            }),
            d: common_vendor.o(($event) => selectStatusOption(option.value), option.value)
          };
        }),
        S: common_vendor.o(resetStatusFilter),
        T: common_vendor.o(completeStatusFilter)
      } : {}) : {}, {
        U: currentTab.value === 0
      }, currentTab.value === 0 ? common_vendor.e({
        V: !isLoading.value && eventList.value.length === 0
      }, !isLoading.value && eventList.value.length === 0 ? common_vendor.e({
        W: common_vendor.p({
          mode: "data",
          text: "暂无活动数据",
          textColor: "#909399",
          iconSize: "120"
        }),
        X: showRetry.value
      }, showRetry.value ? {
        Y: common_vendor.o(fetchEventList),
        Z: common_vendor.p({
          type: "primary",
          size: "normal"
        })
      } : {}) : {}, {
        aa: common_vendor.f(eventList.value, (event, k0, i0) => {
          return {
            a: event.id,
            b: common_vendor.o(($event) => goToDetail(event), event.id),
            c: "8e954d49-8-" + i0,
            d: common_vendor.p({
              event
            })
          };
        }),
        ab: common_vendor.p({
          status: loadMoreStatus.value,
          ["loading-text"]: "正在加载...",
          ["loadmore-text"]: "上拉加载更多",
          ["nomore-text"]: "没有更多了"
        }),
        ac: common_vendor.o(onLoadMore),
        ad: isRefreshing.value,
        ae: common_vendor.o(onRefresh)
      }) : {}, {
        af: currentTab.value === 1
      }, currentTab.value === 1 ? common_vendor.e({
        ag: common_vendor.t(getCurrentLocationTitleCalendar.value),
        ah: showLocationPanel.value ? 1 : "",
        ai: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        aj: common_vendor.o(toggleLocationPanel),
        ak: common_vendor.t(getCurrentTimeTitleCalendar.value),
        al: showTimePanel.value ? 1 : "",
        am: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        an: common_vendor.o(toggleTimePanel),
        ao: showLocationPanel.value
      }, showLocationPanel.value ? common_vendor.e({
        ap: common_vendor.t(allRegionOption.value.label),
        aq: common_vendor.n({
          "active": (currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location) === allRegionOption.value.value
        }),
        ar: common_vendor.o(($event) => selectLocationOption(allRegionOption.value.value)),
        as: common_vendor.f(hotCities.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location) === option.value
            }),
            d: common_vendor.o(($event) => selectLocationOption(option.value), option.value)
          };
        }),
        at: otherCities.value.length > 0
      }, otherCities.value.length > 0 ? {
        av: common_vendor.f(otherCities.value, (city, index, i0) => {
          return {
            a: common_vendor.t(city),
            b: 100 + index,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location) === 100 + index
            }),
            d: common_vendor.o(($event) => selectLocationOption(100 + index), 100 + index)
          };
        })
      } : {}, {
        aw: common_vendor.o(resetLocationFilter),
        ax: common_vendor.o(completeLocationFilter)
      }) : {}, {
        ay: showTimePanel.value
      }, showTimePanel.value ? {
        az: common_vendor.f(options3.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.timeRange : tempFiltersList.value.timeRange) === option.value
            }),
            d: common_vendor.o(($event) => selectTimeOption(option.value), option.value)
          };
        }),
        aA: common_vendor.o(resetTimeFilter),
        aB: common_vendor.o(completeTimeFilter)
      } : {}) : {}, {
        aC: currentTab.value === 1
      }, currentTab.value === 1 ? common_vendor.e({
        aD: !isLoading.value && limitedCalendarEvents.value.length === 0
      }, !isLoading.value && limitedCalendarEvents.value.length === 0 ? common_vendor.e({
        aE: common_vendor.p({
          mode: "data",
          text: "暂无活动数据",
          textColor: "#909399",
          iconSize: "120"
        }),
        aF: showRetry.value
      }, showRetry.value ? {
        aG: common_vendor.o(fetchEventList),
        aH: common_vendor.p({
          type: "primary",
          size: "normal"
        })
      } : {}) : {
        aI: common_vendor.o(goToDetail),
        aJ: common_vendor.o(switchToListView),
        aK: common_vendor.p({
          groups: limitedCalendarEvents.value,
          ["has-more"]: hasMoreCalendarEvents.value,
          ["notch-left"]: calendarNotchLeft.value
        })
      }, {
        aL: common_vendor.o(onLoadMore),
        aM: isRefreshing.value,
        aN: common_vendor.o(onRefresh)
      }) : {}, {
        aO: common_vendor.p({
          current: 2
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8e954d49"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/event/index.js.map
