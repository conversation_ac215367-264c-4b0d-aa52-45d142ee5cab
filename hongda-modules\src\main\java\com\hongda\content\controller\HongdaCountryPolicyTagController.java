package com.hongda.content.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.content.domain.HongdaCountryPolicyTag;
import com.hongda.content.service.IHongdaCountryPolicyTagService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;

/**
 * 政策标签管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@RestController
@RequestMapping("/content/policytag")
public class HongdaCountryPolicyTagController extends BaseController
{
    @Autowired
    private IHongdaCountryPolicyTagService hongdaCountryPolicyTagService;

    /**
     * 查询政策标签管理列表
     */
    @PreAuthorize("@ss.hasPermi('content:policytag:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaCountryPolicyTag hongdaCountryPolicyTag)
    {
        startPage();
        List<HongdaCountryPolicyTag> list = hongdaCountryPolicyTagService.selectHongdaCountryPolicyTagList(hongdaCountryPolicyTag);
        return getDataTable(list);
    }

    /**
     * 导出政策标签管理列表
     */
    @PreAuthorize("@ss.hasPermi('content:policytag:export')")
    @Log(title = "政策标签管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaCountryPolicyTag hongdaCountryPolicyTag)
    {
        List<HongdaCountryPolicyTag> list = hongdaCountryPolicyTagService.selectHongdaCountryPolicyTagList(hongdaCountryPolicyTag);
        ExcelUtil<HongdaCountryPolicyTag> util = new ExcelUtil<HongdaCountryPolicyTag>(HongdaCountryPolicyTag.class);
        util.exportExcel(response, list, "政策标签管理数据");
    }

    /**
     * 获取政策标签管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('content:policytag:query')")
    @GetMapping(value = "/{tagId}")
    public AjaxResult getInfo(@PathVariable("tagId") Long tagId)
    {
        return success(hongdaCountryPolicyTagService.selectHongdaCountryPolicyTagByTagId(tagId));
    }

    /**
     * 新增政策标签管理
     */
    @PreAuthorize("@ss.hasPermi('content:policytag:add')")
    @Log(title = "政策标签管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaCountryPolicyTag hongdaCountryPolicyTag)
    {
        return toAjax(hongdaCountryPolicyTagService.insertHongdaCountryPolicyTag(hongdaCountryPolicyTag));
    }

    /**
     * 修改政策标签管理
     */
    @PreAuthorize("@ss.hasPermi('content:policytag:edit')")
    @Log(title = "政策标签管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaCountryPolicyTag hongdaCountryPolicyTag)
    {
        return toAjax(hongdaCountryPolicyTagService.updateHongdaCountryPolicyTag(hongdaCountryPolicyTag));
    }

    /**
     * 删除政策标签管理
     */
    @PreAuthorize("@ss.hasPermi('content:policytag:remove')")
    @Log(title = "政策标签管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tagIds}")
    public AjaxResult remove(@PathVariable Long[] tagIds)
    {
        return toAjax(hongdaCountryPolicyTagService.deleteHongdaCountryPolicyTagByTagIds(tagIds));
    }
}
