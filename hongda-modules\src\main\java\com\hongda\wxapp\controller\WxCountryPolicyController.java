package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.page.TableDataInfo;
import com.hongda.content.domain.HongdaCountryPolicyArticle;
import com.hongda.content.service.IHongdaCountryPolicyArticleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import com.hongda.common.annotation.Anonymous;

/**
 * 微信小程序 - 国别政策内容接口
 *
 * <AUTHOR>
 */
@Anonymous
@Tag(name = "微信小程序内容接口", description = "提供小程序端获取国别政策相关内容的接口")
@RestController
@RequestMapping("/api/v1/content/countryPolicy")
public class WxCountryPolicyController extends BaseController {

    @Autowired
    private IHongdaCountryPolicyArticleService countryPolicyArticleService;

    /**
     * 查询国别政策文章列表
     * 这个接口是公开的，不需要用户登录即可访问
     */
    @Operation(summary = "查询国别政策文章列表", description = "根据国家ID和政策大类筛选文章列表")
    @GetMapping("/list")
    public TableDataInfo list(HongdaCountryPolicyArticle queryParams) {
        // RuoYi框架的startPage会自动处理分页
        startPage();
        // 调用Service层的方法进行查询
        List<HongdaCountryPolicyArticle> list = countryPolicyArticleService.selectHongdaCountryPolicyArticleList(queryParams);
        // getDataTable会封装分页信息并返回
        return getDataTable(list);
    }

    /**
     * [!] 修正：取消此方法的注释，使其能够处理详情页的请求
     */
    @Operation(summary = "获取国别政策文章详情", description = "根据文章ID获取详细信息")
    @GetMapping("/{articleId}")
    public AjaxResult getInfo(@PathVariable("articleId") Long articleId)
    {
        return success(countryPolicyArticleService.selectHongdaCountryPolicyArticleByArticleId(articleId));
    }
}
