package com.hongda.content.service.impl;

import com.hongda.common.utils.DateUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.content.domain.HongdaCountryPolicyArticle;
import com.hongda.content.domain.HongdaCountryPolicyArticleTag;
import com.hongda.content.domain.HongdaCountryPolicyTag;
import com.hongda.content.mapper.HongdaCountryPolicyArticleMapper;
import com.hongda.content.mapper.HongdaCountryPolicyTagMapper;
import com.hongda.content.service.IHongdaCountryPolicyArticleService;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 国别政策管理Service业务层处理
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class HongdaCountryPolicyArticleServiceImpl implements IHongdaCountryPolicyArticleService
{
    @Autowired
    private HongdaCountryPolicyArticleMapper hongdaCountryPolicyArticleMapper;

    // [移除] 不再需要注入OssFileStorageService，因为URL转换由AOP切面处理

    @Autowired
    private HongdaCountryPolicyTagMapper hongdaCountryPolicyTagMapper;

    // [移除] 不再需要注入URL过期时间
    // @Value("${hongda.oss.url-expire-minutes}")
    // private int urlExpireMinutes;

    @Override
    public HongdaCountryPolicyArticle selectHongdaCountryPolicyArticleByArticleId(Long articleId)
    {
        HongdaCountryPolicyArticle policy = hongdaCountryPolicyArticleMapper.selectHongdaCountryPolicyArticleByArticleId(articleId);
        // [移除] 不再需要手动处理HTML内容加载，AOP切面会自动处理
        // if (policy != null) {
        //     policy.setContent(processHtmlOnLoad(policy.getContent()));
        // }
        return policy;
    }

    @Override
    public List<HongdaCountryPolicyArticle> selectHongdaCountryPolicyArticleList(HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        List<HongdaCountryPolicyArticle> list = hongdaCountryPolicyArticleMapper.selectHongdaCountryPolicyArticleList(hongdaCountryPolicyArticle);
        for (HongdaCountryPolicyArticle article : list) {
            // [移除] 不再需要手动处理HTML内容加载
            // article.setContent(processHtmlOnLoad(article.getContent()));

            // 根据文章ID查询其关联的标签列表 (这部分逻辑保留)
            List<HongdaCountryPolicyTag> tags = hongdaCountryPolicyTagMapper.selectTagsByArticleId(article.getArticleId());
            article.setTags(tags);
        }
        return list;
    }

    @Override
    @Transactional
    public int insertHongdaCountryPolicyArticle(HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        hongdaCountryPolicyArticle.setCreateTime(DateUtils.getNowDate());
        hongdaCountryPolicyArticle.setContent(processHtmlOnSave(hongdaCountryPolicyArticle.getContent()));
        int rows = hongdaCountryPolicyArticleMapper.insertHongdaCountryPolicyArticle(hongdaCountryPolicyArticle);
        insertCountryPolicyArticleTags(hongdaCountryPolicyArticle);
        return rows;
    }

    @Override
    @Transactional
    public int updateHongdaCountryPolicyArticle(HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        hongdaCountryPolicyArticle.setUpdateTime(DateUtils.getNowDate());
        hongdaCountryPolicyArticle.setContent(processHtmlOnSave(hongdaCountryPolicyArticle.getContent()));
        hongdaCountryPolicyArticleMapper.deleteCountryPolicyArticleTagByArticleId(hongdaCountryPolicyArticle.getArticleId());
        insertCountryPolicyArticleTags(hongdaCountryPolicyArticle);
        return hongdaCountryPolicyArticleMapper.updateHongdaCountryPolicyArticle(hongdaCountryPolicyArticle);
    }

    @Override
    @Transactional
    public int deleteHongdaCountryPolicyArticleByArticleIds(Long[] articleIds)
    {
        for (Long articleId : articleIds)
        {
            hongdaCountryPolicyArticleMapper.deleteCountryPolicyArticleTagByArticleId(articleId);
        }
        return hongdaCountryPolicyArticleMapper.deleteHongdaCountryPolicyArticleByArticleIds(articleIds);
    }

    @Override
    @Transactional
    public int deleteHongdaCountryPolicyArticleByArticleId(Long articleId)
    {
        hongdaCountryPolicyArticleMapper.deleteCountryPolicyArticleTagByArticleId(articleId);
        return hongdaCountryPolicyArticleMapper.deleteHongdaCountryPolicyArticleByArticleId(articleId);
    }

    public void insertCountryPolicyArticleTags(HongdaCountryPolicyArticle article)
    {
        Long[] tagIds = article.getTagIds();
        if (StringUtils.isNotEmpty(tagIds))
        {
            List<HongdaCountryPolicyArticleTag> list = new ArrayList<>();
            for (Long tagId : tagIds)
            {
                list.add(new HongdaCountryPolicyArticleTag(article.getArticleId(), tagId));
            }
            if (list.size() > 0)
            {
                hongdaCountryPolicyArticleMapper.batchCountryPolicyArticleTag(list);
            }
        }
    }

    /**
     * [保留] 这个方法在保存时处理HTML，将临时URL转换为objectName，是正确且必要的。
     */
    private String processHtmlOnSave(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return htmlContent;
        }
        Document doc = Jsoup.parseBodyFragment(htmlContent);
        Elements images = doc.select("img[data-href^=oss-object-name://]");
        for (Element img : images) {
            String dataHref = img.attr("data-href");
            String objectName = dataHref.substring("oss-object-name://".length());
            img.attr("data-oss-object-name", objectName);
            img.removeAttr("src");
            img.removeAttr("data-href");
        }
        return doc.body().html();
    }

    // [移除] processHtmlOnLoad 方法已不再需要
}