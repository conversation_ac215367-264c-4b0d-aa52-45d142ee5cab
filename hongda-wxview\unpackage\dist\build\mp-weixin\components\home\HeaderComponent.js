"use strict";const e=require("../../common/vendor.js");if(!Array){e.resolveComponent("uni-icons")()}Math;const t={__name:"HeaderComponent",emits:["height-calculated"],setup(t,{emit:n}){const a=e.index.upx2px(24),o=e.index.upx2px(10),s=e.ref({}),i=e.ref({}),r=n;e.onMounted((()=>{const t=e.index.getMenuButtonBoundingClientRect(),n=e.index.getSystemInfoSync(),u=2*(t.top-n.statusBarHeight)+t.height+2*o,c=t.left-a-10,p=t.height+0,h=u+n.statusBarHeight;s.value={height:`${h}px`,paddingTop:`${n.statusBarHeight}px`},i.value={width:`${c}px`,height:`${p}px`},r("height-calculated",h)}));const u=()=>{e.index.navigateTo({url:"/pages_sub/pages_other/search"})};return(t,n)=>({a:e.p({type:"search",size:"18",color:"#606266"}),b:e.s(i.value),c:e.o(u),d:e.s(s.value)})}},n=e._export_sfc(t,[["__scopeId","data-v-10a54182"]]);wx.createComponent(n);
