{"version": 3, "file": "EventActionBar.js", "sources": ["components/event/EventActionBar.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRBY3Rpb25CYXIudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"bottom-action-bar\" v-if=\"!isLoading && eventDetail\">\r\n    <up-button\r\n      open-type=\"share\"\r\n      @click=\"$emit('share')\"\r\n      :customStyle=\"{\r\n        width: '214rpx',\r\n        height: '76rpx',\r\n        margin: '0',\r\n        padding: '0',\r\n        border: 'none',\r\n        backgroundColor: 'rgba(42, 97, 241, 0.2)',\r\n        borderRadius: '38rpx'\r\n      }\"\r\n    >\r\n      <view class=\"share-button-content\">\r\n        <image class=\"share-icon\" :src=\"shareIconUrl\" mode=\"aspectFit\"></image>\r\n        <text class=\"share-text\">分享</text>\r\n      </view>\r\n    </up-button>\r\n\r\n    <up-button\r\n      type=\"primary\"\r\n      :disabled=\"isButtonDisabled\"\r\n      @click=\"$emit('register')\"\r\n      :customStyle=\"{\r\n        width: '464rpx',\r\n        height: '76rpx',\r\n        margin: '0',\r\n        backgroundColor: registrationStatus === 'registered' ? '#C4CFD1' : (isButtonDisabled ? '#cccccc' : '#023F98'),\r\n        border: 'none',\r\n        borderRadius: '38rpx',\r\n        color: '#ffffff',\r\n        fontFamily: 'Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif',\r\n        fontWeight: 'normal',\r\n        fontSize: '28rpx'\r\n      }\"\r\n    >\r\n      {{ buttonText }}\r\n    </up-button>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\n\r\ndefineProps({\r\n  eventDetail: { type: Object, required: true },\r\n  isLoading: { type: Boolean, default: false },\r\n  registrationStatus: { type: String, required: true },\r\n  isButtonDisabled: { type: Boolean, required: true },\r\n  buttonText: { type: String, required: true }\r\n})\r\n\r\n// 静态资源 URL（不再使用本地兜底）\r\nconst shareIconUrl = ref('')\r\n\r\n// 组件挂载时读取静态资源配置\r\nonMounted(() => {\r\n  const assets = uni.getStorageSync('staticAssets')\r\n  \r\n  shareIconUrl.value = assets?.detail_icon_share || ''\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bottom-action-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n  z-index: 100;\r\n  height: calc(156rpx + env(safe-area-inset-bottom));\r\n  background-color: #FFFFFF;\r\n  border-top: 2rpx solid #EEEEEE;\r\n  border-radius: 0;\r\n  box-shadow: none;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n.share-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  margin-right: 8rpx;\r\n}\r\n\r\n.share-text {\r\n  width: 56rpx;\r\n  height: 44rpx;\r\n  font-family: \"Alibaba PuHuiTi 3.0\", \"Alibaba PuHuiTi 30\", sans-serif;\r\n  font-weight: normal;\r\n  font-size: 28rpx;\r\n  color: #023F98;\r\n  line-height: 44rpx;\r\n  text-align: left;\r\n  font-style: normal;\r\n  text-transform: none;\r\n}\r\n\r\n.share-button-content {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n:deep(.up-button--square) {\r\n  border-radius: 10rpx !important;\r\n}\r\n\r\n:deep(.up-button--primary) {\r\n  border-radius: 44rpx !important;\r\n}\r\n</style>\r\n\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventActionBar.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAuDA,UAAM,eAAeA,cAAG,IAAC,EAAE;AAG3BC,kBAAAA,UAAU,MAAM;AACd,YAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAEhD,mBAAa,SAAQ,iCAAQ,sBAAqB;AAAA,IACpD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7DD,GAAG,gBAAgB,SAAS;"}