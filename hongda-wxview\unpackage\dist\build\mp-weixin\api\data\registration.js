"use strict";const t=require("../../utils/request.js"),e=()=>t.http.get("/registration/my-registrations");exports.cancelRegistrationApi=e=>t.http.post("/registration/cancel",e),exports.getFormDefinitionApi=e=>t.http.get(`/content/form-definition/event/${e}`),exports.getMyRegistrationDetailApi=async t=>{try{console.log("查找报名详情，目标 eventId:",t,"类型:",typeof t);const o=await e();if(200===o.code&&o.data){console.log("所有报名记录:",o.data);const e=Number(t),n=o.data.find((t=>{const o=Number(t.eventId);return console.log("比较 eventId:",o,"vs",e,"匹配:",o===e),o===e}));return console.log("找到的报名记录:",n),n?{code:200,data:n,msg:"获取成功"}:(console.warn("未找到匹配的报名记录，可能的原因:"),console.warn("- eventId 类型不匹配"),console.warn("- 用户确实未报名此活动"),console.warn("- 数据库记录异常"),{code:404,data:null,msg:"未找到该活动的报名记录"})}return console.warn("获取所有报名记录失败:",o),o}catch(o){throw console.error("获取报名详情失败:",o),o}},exports.getMyRegistrationsApi=e;
