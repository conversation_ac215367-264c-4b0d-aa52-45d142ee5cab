package com.hongda.content.service.impl;

import com.hongda.common.utils.DateUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.content.domain.HongdaPark;
import com.hongda.content.mapper.HongdaParkMapper;
import com.hongda.content.service.IHongdaParkService;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 园区信息Service业务层处理
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class HongdaParkServiceImpl implements IHongdaParkService
{
    @Autowired
    private HongdaParkMapper hongdaParkMapper;

    // [移除] 不再需要注入OSS服务和URL过期时间
    // @Autowired
    // private OssFileStorageService ossFileStorageService;
    //
    // @Value("${hongda.oss.url-expire-minutes}")
    // private int urlExpireMinutes;

    @Override
    public HongdaPark selectHongdaParkById(Long id)
    {
        HongdaPark park = hongdaParkMapper.selectHongdaParkById(id);
        // [移除] 不再需要手动处理HTML内容加载，AOP切面会自动处理
        // if (park != null) {
        //     park.setSummary(processHtmlOnLoad(park.getSummary()));
        //     park.setContent(processHtmlOnLoad(park.getContent()));
        // }
        return park;
    }

    @Override
    public List<HongdaPark> selectHongdaParkList(HongdaPark hongdaPark)
    {
        // [移除] 不再需要遍历处理富文本字段
        return hongdaParkMapper.selectHongdaParkList(hongdaPark);
    }

    @Override
    public int insertHongdaPark(HongdaPark hongdaPark)
    {
        hongdaPark.setCreateTime(DateUtils.getNowDate());
        // [保留] 保存前处理富文本字段是必要的
        hongdaPark.setSummary(processHtmlOnSave(hongdaPark.getSummary()));
        hongdaPark.setContent(processHtmlOnSave(hongdaPark.getContent()));
        return hongdaParkMapper.insertHongdaPark(hongdaPark);
    }

    @Override
    public int updateHongdaPark(HongdaPark hongdaPark)
    {
        hongdaPark.setUpdateTime(DateUtils.getNowDate());
        // [保留] 更新前处理富文本字段是必要的
        hongdaPark.setSummary(processHtmlOnSave(hongdaPark.getSummary()));
        hongdaPark.setContent(processHtmlOnSave(hongdaPark.getContent()));
        return hongdaParkMapper.updateHongdaPark(hongdaPark);
    }

    @Override
    public int deleteHongdaParkByIds(Long[] ids)
    {
        return hongdaParkMapper.deleteHongdaParkByIds(ids);
    }

    @Override
    public int deleteHongdaParkById(Long id)
    {
        return hongdaParkMapper.deleteHongdaParkById(id);
    }

    /**
     * [保留] 这个方法在保存时处理HTML，将临时URL转换为objectName，是正确且必要的。
     */
    private String processHtmlOnSave(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return htmlContent;
        }
        Document doc = Jsoup.parseBodyFragment(htmlContent);
        Elements images = doc.select("img[data-href^=oss-object-name://]");
        for (Element img : images) {
            String dataHref = img.attr("data-href");
            String objectName = dataHref.substring("oss-object-name://".length());
            img.attr("data-oss-object-name", objectName);
            img.removeAttr("src");
            img.removeAttr("data-href");
        }
        return doc.body().html();
    }

    // [移除] processHtmlOnLoad 方法已不再需要
}