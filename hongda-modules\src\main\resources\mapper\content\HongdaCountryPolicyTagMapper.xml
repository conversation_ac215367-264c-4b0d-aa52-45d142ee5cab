<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaCountryPolicyTagMapper">
    
    <resultMap type="HongdaCountryPolicyTag" id="HongdaCountryPolicyTagResult">
        <result property="tagId"    column="tag_id"    />
        <result property="tagName"    column="tag_name"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectHongdaCountryPolicyTagVo">
        select tag_id, tag_name, sort_order, create_by, create_time, update_by, update_time, remark from hongda_country_policy_tag
    </sql>

    <select id="selectHongdaCountryPolicyTagList" parameterType="HongdaCountryPolicyTag" resultMap="HongdaCountryPolicyTagResult">
        <include refid="selectHongdaCountryPolicyTagVo"/>
        <where>  
            <if test="tagName != null  and tagName != ''"> and tag_name like concat('%', #{tagName}, '%')</if>
        </where>
    </select>
    
    <select id="selectHongdaCountryPolicyTagByTagId" parameterType="Long" resultMap="HongdaCountryPolicyTagResult">
        <include refid="selectHongdaCountryPolicyTagVo"/>
        where tag_id = #{tagId}
    </select>

    <insert id="insertHongdaCountryPolicyTag" parameterType="HongdaCountryPolicyTag" useGeneratedKeys="true" keyProperty="tagId">
        insert into hongda_country_policy_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">#{tagName},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateHongdaCountryPolicyTag" parameterType="HongdaCountryPolicyTag">
        update hongda_country_policy_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name = #{tagName},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where tag_id = #{tagId}
    </update>

    <delete id="deleteHongdaCountryPolicyTagByTagId" parameterType="Long">
        delete from hongda_country_policy_tag where tag_id = #{tagId}
    </delete>

    <delete id="deleteHongdaCountryPolicyTagByTagIds" parameterType="String">
        delete from hongda_country_policy_tag where tag_id in 
        <foreach item="tagId" collection="array" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </delete>

    <select id="selectTagsByArticleId" parameterType="Long" resultMap="HongdaCountryPolicyTagResult">
        select t.tag_id, t.tag_name, t.sort_order
        from hongda_country_policy_tag t
                 inner join hongda_country_policy_article_tag_relation r on t.tag_id = r.tag_id
        where r.article_id = #{articleId}
        order by t.sort_order asc
    </select>

    <select id="countArticleRelationsByTagId" parameterType="Long" resultType="Integer">
        select count(*) from hongda_country_policy_article_tag_relation where tag_id = #{tagId}
    </select>
</mapper>