"use strict";const e=require("../../common/vendor.js"),t=require("../../api/platform/nav.js"),a=require("../../utils/navigation.js"),o={__name:"QuickNavigation",setup(o){const n=e.ref([]);return e.onMounted((()=>{(async()=>{try{const e=await t.getNavListByPosition("HOME_QUICK_NAV");e&&e.data&&(n.value=e.data)}catch(e){console.error("获取快捷导航失败:",e)}})()})),(t,o)=>e.e({a:n.value.length>0},n.value.length>0?{b:e.f(n.value,((t,o,n)=>({a:t.iconUrl,b:e.t(t.title),c:t.id,d:e.o((e=>{return o=t,void a.navigateTo(o);var o}),t.id)})))}:{})}},n=e._export_sfc(o,[["__scopeId","data-v-e05aef53"]]);wx.createComponent(n);
