"use strict";const e=require("../../common/vendor.js");require("../../utils/config.js");const t=require("../../utils/date.js"),n=require("../../utils/location.js"),r={__name:"EventInfoCard",props:{eventDetail:{type:Object,required:!0}},setup(r){const a=r,u=e.ref(""),o=e.ref(""),l=e.ref(""),i=e.ref("");e.onMounted((()=>{const t=e.index.getStorageSync("staticAssets");u.value=(null==t?void 0:t.detail_bg)||"",o.value=(null==t?void 0:t.detail_icon_time)||"",l.value=(null==t?void 0:t.detail_icon_location)||"",i.value=(null==t?void 0:t.detail_icon_user)||""}));const s=e.computed((()=>a.eventDetail||{})),c=e.computed((()=>{var e,t;const n=null==(e=s.value)?void 0:e.registrationStartTime,r=null==(t=s.value)?void 0:t.registrationEndTime;try{const e=new Date;return n&&e<new Date(n)?0:(!n||e>=new Date(n))&&(!r||e<=new Date(r))?1:r&&e>new Date(r)?2:1}catch(a){return console.warn("报名状态计算失败:",a),1}})),d=e=>{switch(e){case 0:return"即将开始";case 1:return"报名中";case 2:return"报名截止";default:return"未知"}},v=e=>{switch(e){case 0:return"not-started";case 1:return"open";case 2:return"ended";default:return"unknown"}},m=e.computed((()=>{var e,n;if(!(null==(e=s.value)?void 0:e.startTime)||!(null==(n=s.value)?void 0:n.endTime))return"时间待定";try{const e=t.formatDate(s.value.startTime,"YYYY-MM-DD HH:mm");return`${e} 至 ${t.formatDate(s.value.endTime,"YYYY-MM-DD HH:mm")}`}catch(r){return console.warn("时间格式化失败:",r),"时间格式错误"}})),p=e.computed((()=>{if(!s.value)return[{type:"text",text:"加载中..."}];const e=Number(s.value.maxParticipants)||0;if(0===e)return[{type:"text",text:"剩余名额: 不限人数"}];const t=Number(s.value.registeredCount)||0,n=Math.max(0,e-t);return[{type:"node",name:"span",children:[{type:"text",text:"剩余名额: "},{type:"node",name:"span",attrs:{style:"color: #023F98;"},children:[{type:"text",text:String(n)}]},{type:"text",text:`/${e}`}]}]}));return(t,r)=>({a:u.value,b:e.t(d(c.value)),c:e.n(v(c.value)),d:e.t(s.value.title||""),e:o.value,f:e.t(m.value),g:l.value,h:e.t(e.unref(n.formatEventLocation)(s.value)),i:i.value,j:p.value})}},a=e._export_sfc(r,[["__scopeId","data-v-fba015d8"]]);wx.createComponent(a);
