package com.hongda.content.service.impl;

import com.hongda.common.utils.DateUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.content.domain.HongdaArticle;
import com.hongda.content.domain.HongdaArticleTagRelation;
import com.hongda.content.mapper.HongdaArticleMapper;
import com.hongda.content.service.IHongdaArticleService;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 资讯文章Service业务层处理
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class HongdaArticleServiceImpl implements IHongdaArticleService {
    @Autowired
    private HongdaArticleMapper hongdaArticleMapper;

    // [移除] 不再需要注入OssFileStorageService
    // [移除] 不再需要注入URL过期时间

    @Override
    public HongdaArticle selectHongdaArticleById(Long id) {
        HongdaArticle article = hongdaArticleMapper.selectHongdaArticleById(id);
        if (article != null) {
            article.setTagIds(hongdaArticleMapper.selectTagIdsByArticleId(id));
            // [移除] 不再需要手动处理HTML内容加载
            // article.setContent(processHtmlOnLoad(article.getContent()));
        }
        return article;
    }

    @Override
    public List<HongdaArticle> selectHongdaArticleList(HongdaArticle hongdaArticle) {
        // [移除] 不再需要手动处理HTML内容加载
        return hongdaArticleMapper.selectHongdaArticleList(hongdaArticle);
    }

    @Override
    @Transactional
    public int insertHongdaArticle(HongdaArticle hongdaArticle) {
        hongdaArticle.setCreateTime(DateUtils.getNowDate());
        hongdaArticle.setContent(processHtmlOnSave(hongdaArticle.getContent()));
        int rows = hongdaArticleMapper.insertHongdaArticle(hongdaArticle);
        insertArticleTag(hongdaArticle);
        return rows;
    }

    @Override
    @Transactional
    public int updateHongdaArticle(HongdaArticle hongdaArticle) {
        hongdaArticle.setUpdateTime(DateUtils.getNowDate());
        hongdaArticle.setContent(processHtmlOnSave(hongdaArticle.getContent()));
        hongdaArticleMapper.deleteArticleTagByArticleId(hongdaArticle.getId());
        insertArticleTag(hongdaArticle);
        return hongdaArticleMapper.updateHongdaArticle(hongdaArticle);
    }

    private void insertArticleTag(HongdaArticle hongdaArticle) {
        List<Long> tagIds = hongdaArticle.getTagIds();
        if (tagIds != null && !tagIds.isEmpty()) {
            List<HongdaArticleTagRelation> list = new ArrayList<>();
            for (Long tagId : tagIds) {
                HongdaArticleTagRelation relation = new HongdaArticleTagRelation();
                relation.setArticleId(hongdaArticle.getId());
                relation.setTagId(tagId);
                list.add(relation);
            }
            if (!list.isEmpty()) {
                hongdaArticleMapper.batchArticleTag(list);
            }
        }
    }

    @Override
    @Transactional
    public int deleteHongdaArticleByIds(Long[] ids) {
        for (Long id : ids) {
            hongdaArticleMapper.deleteArticleTagByArticleId(id);
        }
        return hongdaArticleMapper.deleteHongdaArticleByIds(ids);
    }

    @Override
    @Transactional
    public int deleteHongdaArticleById(Long id) {
        hongdaArticleMapper.deleteArticleTagByArticleId(id);
        return hongdaArticleMapper.deleteHongdaArticleById(id);
    }

    @Override
    public void incrementArticleViewCount(Long articleId) {
        if (articleId != null && articleId > 0) {
            hongdaArticleMapper.incrementViewCount(articleId);
        }
    }

    @Override
    public List<HongdaArticle> selectPublishedArticleListForMiniProgram(HongdaArticle hongdaArticle) {
        // [移除] 不再需要手动处理HTML内容加载
        return hongdaArticleMapper.selectPublishedArticleListForMiniProgram(hongdaArticle);
    }

    @Override
    public HongdaArticle selectPublishedArticleByIdForMiniProgram(Long id) {
        HongdaArticle article = hongdaArticleMapper.selectPublishedArticleByIdForMiniProgram(id);
        // [移除] 不再需要手动处理HTML内容加载
        // if (article != null) {
        //     article.setContent(processHtmlOnLoad(article.getContent()));
        // }
        return article;
    }

    /**
     * [保留] 这个方法在保存时处理HTML，将临时URL转换为objectName，是正确且必要的。
     */
    private String processHtmlOnSave(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return htmlContent;
        }
        Document doc = Jsoup.parseBodyFragment(htmlContent);
        Elements images = doc.select("img[data-href^=oss-object-name://]");
        for (Element img : images) {
            String dataHref = img.attr("data-href");
            String objectName = dataHref.substring("oss-object-name://".length());
            img.attr("data-oss-object-name", objectName);
            img.removeAttr("src");
            img.removeAttr("data-href");
        }
        return doc.body().html();
    }

    // [移除] processHtmlOnLoad 方法已不再需要
}