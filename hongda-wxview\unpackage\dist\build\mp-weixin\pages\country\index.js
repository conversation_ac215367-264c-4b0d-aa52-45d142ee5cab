"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/country.js"),l=require("../../utils/config.js");if(!Array){(e.resolveComponent("uni-search-bar")+e.resolveComponent("uni-load-more"))()}Math||((()=>"../../uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+u)();const u=()=>"../../components/layout/CustomTabBar.js",t={__name:"index",setup(u){const t=e.computed((()=>d.value.bg_country_list_header||"")),n=e.computed((()=>d.value.bg_country_list_active_tab||"")),o=l.IMAGE_BASE_URL,r=e.ref(""),i=e.ref("ALL"),s=e.ref([]),v=e.ref(!1),c=e.getCurrentInstance(),d=e.ref(e.index.getStorageSync("staticAssets")||{}),f=[{label:"全部",value:"ALL"},{label:"亚洲",value:"ASIA"},{label:"欧洲",value:"EUROPE"},{label:"北美洲",value:"NORTH_AMERICA"},{label:"南美洲",value:"SOUTH_AMERICA"},{label:"非洲",value:"AFRICA"},{label:"大洋洲",value:"OCEANIA"}],b=e.ref(0),m=e.ref([]),p=e.ref(0),g=e.computed((()=>{if(!m.value||0===m.value.length)return-999;const a=m.value[b.value];if(!a)return-999;const l=e.index.upx2px(20);return a.left-p.value+a.width/2-l})),_=e=>{p.value=e.detail.scrollLeft},h=async(l=!1)=>{if(!v.value){v.value=!0;try{const u=await a.getCountryList({continent:i.value,keyword:r.value});s.value=l?u.data:[...s.value,...u.data]}catch(u){console.error("获取国别列表失败:",u),e.index.showToast({title:"数据加载失败",icon:"none"})}finally{v.value=!1,l&&e.index.stopPullDownRefresh()}}},A=()=>{h(!0)},x=()=>{r.value="",h(!0)},y=()=>{};return e.onLoad((()=>{h(!0),setTimeout((()=>{e.nextTick$1((()=>{e.index.createSelectorQuery().in(c).selectAll(".tab-item").boundingClientRect((e=>{e&&e.length&&(m.value=e)})).exec()}))}),150)})),e.onShow((()=>{e.index.hideTabBar()})),e.onPullDownRefresh((()=>{h(!0)})),(a,l)=>e.e({a:e.o(A),b:e.o(x),c:e.o(A),d:e.o((e=>r.value=e)),e:e.p({placeholder:"搜索国别名称",radius:"100",bgColor:"#ffffff",modelValue:r.value}),f:e.f(f,((a,l,u)=>({a:e.t(a.label),b:a.value,c:e.n({active:i.value===a.value}),d:e.o((e=>((e,a)=>{i.value!==e&&(i.value=e,b.value=a,h(!0))})(a.value,l)),a.value),e:"tab-"+l,f:i.value===a.value?`url(${n.value})`:"none"}))),g:e.o(_),h:g.value+"px",i:`url(${t.value})`,j:e.f(s.value,((a,l,u)=>({a:e.unref(o)+a.listCoverUrl,b:e.t(a.nameCn),c:e.t(a.nameEn),d:e.t(a.summary),e:e.unref(o)+a.flagUrl,f:a.id,g:e.o((l=>{return u=a.id,void e.index.navigateTo({url:`/pages_sub/pages_country/detail?id=${u}`});var u}),a.id)}))),k:v.value},v.value?{l:e.p({status:"loading"})}:(s.value.length,{}),{m:0===s.value.length,n:e.o(y),o:e.p({current:3})})}},n=e._export_sfc(t,[["__scopeId","data-v-52cab17d"]]);wx.createPage(n);
