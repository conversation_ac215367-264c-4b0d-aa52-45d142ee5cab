package com.hongda.framework.security.filter;

import com.hongda.common.core.domain.model.LoginUser;
import com.hongda.common.utils.SecurityUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.framework.web.service.TokenService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * token过滤器 验证token有效性
 *
 * <AUTHOR> (modified by Gemini)
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    @Autowired
    private TokenService tokenService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        
        // 1. 尝试从请求中获取LoginUser
        // 这里的 getLoginUser 方法内部应该已经处理了Token的解析、验证、异常等
        LoginUser loginUser = tokenService.getLoginUser(request);

        // 2. 如果成功获取到用户信息，并且当前安全上下文中没有认证信息，则设置认证信息
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
            // 刷新令牌有效期
            tokenService.verifyToken(loginUser);
            // 创建认证令牌并设置到Spring Security上下文中
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        }

        // 3. 无论是否认证成功，都必须继续执行过滤器链，让请求到达目标接口
        // 真正的权限控制（即这个接口是否需要登录）将由 SecurityConfig 来决定
        chain.doFilter(request, response);
    }
}
