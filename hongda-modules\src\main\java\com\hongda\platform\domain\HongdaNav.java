package com.hongda.platform.domain;

import com.hongda.common.annotation.Excel;
import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Transient;

/**
 * 导航配置对象 hongda_nav
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Getter
@Setter
@ToString
@Schema(description = "导航配置")
public class HongdaNav extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 导航ID */
    @Schema(description = "导航ID", example = "1")
    private Long id;

    /** 导航标题 */
    @Excel(name = "导航标题")
    @Schema(description = "导航标题", example = "活动报名")
    private String title;

    /** 图标链接 */
    @Schema(description = "图标链接", example = "https://example.com/icon.png")
    @OssUrl
    private String iconUrl;

    /** 链接类型 */
    @Excel(name = "链接类型")
    @Schema(description = "链接类型 (例如: ARTICLE, EVENT, PAGE, EXTERNAL)", example = "PAGE")
    private String linkType;

    /** 链接目标 */
    @Excel(name = "链接目标")
    @Schema(description = "链接目标 (ID, 页面路径或完整URL)", example = "/pages/event/index")
    private String linkTarget;

    /** 导航位置代码 */
    @Excel(name = "导航位置代码")
    @Schema(description = "导航位置代码", example = "HOME_NAV")
    private String positionCode;

    /** 排序 */
    @Excel(name = "排序")
    @Schema(description = "排序", example = "1")
    private Long sortOrder;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "1=启用,0=禁用")
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;

    // [新增] 增加一个瞬态字段来接收关联查询的标题
    /**
     * 链接目标的标题 (非数据表字段)
     * 用于在列表展示关联内容的标题或名称
     */
    @Schema(description = "链接目标的标题 (仅用于列表展示)")
    @Transient // 表示这是一个非数据库表字段
    private String linkTargetTitle;
}