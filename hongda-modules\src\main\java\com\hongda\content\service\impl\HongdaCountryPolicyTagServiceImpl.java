package com.hongda.content.service.impl;

import com.hongda.common.exception.ServiceException;
import com.hongda.common.utils.DateUtils;
import com.hongda.content.domain.HongdaCountryPolicyTag;
import com.hongda.content.mapper.HongdaCountryPolicyTagMapper;
import com.hongda.content.service.IHongdaCountryPolicyTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 政策标签管理Service业务层处理
 * * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class HongdaCountryPolicyTagServiceImpl implements IHongdaCountryPolicyTagService
{
    @Autowired
    private HongdaCountryPolicyTagMapper hongdaCountryPolicyTagMapper;

    /**
     * 查询政策标签管理
     * * @param tagId 政策标签管理主键
     * @return 政策标签管理
     */
    @Override
    public HongdaCountryPolicyTag selectHongdaCountryPolicyTagByTagId(Long tagId)
    {
        return hongdaCountryPolicyTagMapper.selectHongdaCountryPolicyTagByTagId(tagId);
    }

    /**
     * 查询政策标签管理列表
     * * @param hongdaCountryPolicyTag 政策标签管理
     * @return 政策标签管理
     */
    @Override
    public List<HongdaCountryPolicyTag> selectHongdaCountryPolicyTagList(HongdaCountryPolicyTag hongdaCountryPolicyTag)
    {
        return hongdaCountryPolicyTagMapper.selectHongdaCountryPolicyTagList(hongdaCountryPolicyTag);
    }

    /**
     * 新增政策标签管理
     * * @param hongdaCountryPolicyTag 政策标签管理
     * @return 结果
     */
    @Override
    public int insertHongdaCountryPolicyTag(HongdaCountryPolicyTag hongdaCountryPolicyTag)
    {
        hongdaCountryPolicyTag.setCreateTime(DateUtils.getNowDate());
        return hongdaCountryPolicyTagMapper.insertHongdaCountryPolicyTag(hongdaCountryPolicyTag);
    }

    /**
     * 修改政策标签管理
     * * @param hongdaCountryPolicyTag 政策标签管理
     * @return 结果
     */
    @Override
    public int updateHongdaCountryPolicyTag(HongdaCountryPolicyTag hongdaCountryPolicyTag)
    {
        hongdaCountryPolicyTag.setUpdateTime(DateUtils.getNowDate());
        return hongdaCountryPolicyTagMapper.updateHongdaCountryPolicyTag(hongdaCountryPolicyTag);
    }

    /**
     * [修改] 批量删除政策标签管理
     * * @param tagIds 需要删除的政策标签管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaCountryPolicyTagByTagIds(Long[] tagIds)
    {
        for (Long tagId : tagIds)
        {
            // 在删除前进行检查
            checkTagHasRelations(tagId);
        }
        return hongdaCountryPolicyTagMapper.deleteHongdaCountryPolicyTagByTagIds(tagIds);
    }

    /**
     * [修改] 删除政策标签管理信息
     * * @param tagId 政策标签管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaCountryPolicyTagByTagId(Long tagId)
    {
        // 在删除前进行检查
        checkTagHasRelations(tagId);
        return hongdaCountryPolicyTagMapper.deleteHongdaCountryPolicyTagByTagId(tagId);
    }

    /**
     * [新增] 检查标签是否有关联的文章，如果有关联则抛出异常
     *
     * @param tagId 标签ID
     */
    private void checkTagHasRelations(Long tagId)
    {
        int count = hongdaCountryPolicyTagMapper.countArticleRelationsByTagId(tagId);
        if (count > 0)
        {
            // 查询标签名称，用于更友好的提示
            HongdaCountryPolicyTag tag = hongdaCountryPolicyTagMapper.selectHongdaCountryPolicyTagByTagId(tagId);
            String tagName = (tag != null) ? tag.getTagName() : "";
            throw new ServiceException(String.format("标签 [%s] 已被 %d 篇文章关联，无法删除", tagName, count));
        }
    }
}