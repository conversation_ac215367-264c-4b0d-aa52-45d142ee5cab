{"version": 3, "file": "detail.js", "sources": ["pages_sub/pages_article/detail.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX2FydGljbGVcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view v-if=\"article\" class=\"page-container\">\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\"\r\n            :style=\"{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <u-icon name=\"arrow-left\" color=\"#000000\" size=\"22\"></u-icon>\r\n        </view>\r\n        <view class=\"nav-title\">资讯详情</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n      <view class=\"hero-section\">\r\n        <image class=\"hero-image\" :src=\"getFullImageUrl(article.coverImageUrl)\" mode=\"aspectFill\"></image>\r\n      </view>\r\n\r\n      <view class=\"main-content\">\r\n        <view class=\"article-title\">{{ article.title }}</view>\r\n\r\n        <view class=\"article-meta-new\">\r\n          <view class=\"meta-left\">\r\n            <text class=\"meta-text\">{{ article.source }}</text>\r\n            <text class=\"meta-text\">{{ formatDate(article.publishTime, 'YYYY-MM-DD') }}</text>\r\n          </view>\r\n          <view class=\"meta-right\">\r\n            <text class=\"meta-text\">{{ article.viewCount }} 次阅读</text>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"summary-card\" v-if=\"article.summary\" :style=\"summaryCardStyle\">\r\n          <text class=\"summary-text\">{{ article.summary }}</text>\r\n        </view>\r\n\r\n        <view class=\"content-card\">\r\n          <view class=\"content-body\">\r\n            <mp-html :content=\"article.content\" :domain=\"imageBaseUrl\" :tag-style=\"tagStyle\" :preview-img=\"true\"\r\n                     lazy-load/>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"tags-section-new\" v-if=\"article.parsedTags && article.parsedTags.length > 0\">\r\n        <text class=\"tags-label\">分类：</text>\r\n        <text v-for=\"(tag, index) in article.parsedTags\" :key=\"tag.id || tag.name\" class=\"tag-item-new\">\r\n          {{ tag.name || tag }}{{ index < article.parsedTags.length - 1 ? ' / ' : '' }}\r\n        </text>\r\n      </view>\r\n\r\n      <view class=\"comment-container\">\r\n        <view class=\"comment-header-section\">\r\n          <text class=\"comment-main-title\">留言 ({{ commentTotal }})</text>\r\n        </view>\r\n        <view class=\"comment-input-card\" @click=\"handleOpenCommentInput\">\r\n          <image\r\n              class=\"comment-input-icon\"\r\n              :src=\"commentInputIconUrl\"\r\n          ></image>\r\n          <text class=\"comment-input-placeholder\">写留言</text>\r\n        </view>\r\n\r\n        <view v-if=\"commentList.length > 0\" class=\"comment-list-container\">\r\n          <CommentItem\r\n              v-for=\"comment in commentList\"\r\n              :key=\"comment.id\"\r\n              :comment=\"comment\"\r\n          />\r\n        </view>\r\n        <view v-else class=\"empty-state\">\r\n          <view class=\"empty-icon\">💬</view>\r\n          <text class=\"empty-title\">暂无评论</text>\r\n          <text class=\"empty-desc\">成为第一个发表看法的人吧</text>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n\r\n\r\n  </view>\r\n\r\n  <view v-else-if=\"loadError\" class=\"error-state\">\r\n    <view class=\"empty-icon\">⚠️</view>\r\n    <text class=\"empty-title\">加载失败</text>\r\n    <text class=\"empty-desc\">无法获取文章内容，请稍后重试</text>\r\n    <button class=\"retry-btn\" @click=\"retryLoad\">重新加载</button>\r\n  </view>\r\n\r\n  <u-popup :show=\"showNewCommentModal\" @close=\"closeNewCommentModal\" mode=\"bottom\" round=\"20\" :safe-area-inset-bottom=\"true\">\r\n    <view class=\"comment-popup\">\r\n      <view class=\"popup-header\">\r\n        <text class=\"popup-title\">发表您的看法</text>\r\n        <text class=\"popup-close\" @click=\"closeNewCommentModal\">×</text>\r\n      </view>\r\n      <view class=\"popup-body\">\r\n      <textarea\r\n          class=\"comment-input\"\r\n          v-model=\"newComment.content\"\r\n          placeholder=\"分享你的想法...\"\r\n          :auto-height=\"true\"\r\n          maxlength=\"300\"\r\n      ></textarea>\r\n        <view class=\"popup-footer\">\r\n          <text class=\"comment-counter\">{{ newComment.content.length }}/300</text>\r\n          <button\r\n              class=\"comment-submit\"\r\n              :class=\"{'comment-submit-active': newComment.content.trim()}\"\r\n              :disabled=\"!newComment.content.trim() || newComment.isSubmitting\"\r\n              @click=\"postNewComment\"\r\n          >\r\n            {{ newComment.isSubmitting ? '发布中...' : '发布' }}\r\n          </button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </u-popup>\r\n</template>\r\n\r\n<script setup>\r\nimport { computed, nextTick, reactive, ref } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue';\r\nimport { getArticleDetail } from '@/api/content/article.js';\r\nimport { addComment, getCommentList } from '@/pages_sub/pages_article/api/content/comment.js';\r\nimport { tagStyle } from '@/pages_sub/pages_article/api/common/mpHtmlStyles.js';\r\nimport config from '@/utils/config.js';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\nimport { formatDate } from '@/utils/date.js';\r\nimport CommentItem from '@/components/common/CommentItem.vue';\r\n\r\n\r\nconst navBarPaddingBottomRpx = 20;\r\nconst navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\nconst showNewCommentModal = ref(false);\r\nconst loadError = ref(false); // 新增加载错误状态\r\nconst assets = ref(uni.getStorageSync('staticAssets') || {});\r\n\r\nconst commentInputIconUrl = computed(() => {\r\n  // 使用我们约定的“暗号” icon_article_comment_input\r\n  return assets.value.icon_article_comment_input || '';\r\n});\r\n\r\n// 3. 为摘要卡片背景创建计算属性\r\nconst summaryCardStyle = computed(() => {\r\n  // 使用我们约定的“暗号” bg_article_summary_card\r\n  const imageUrl = assets.value.bg_article_summary_card;\r\n  if (imageUrl) {\r\n    // 成功获取到URL时，才生成样式对象\r\n    return {\r\n      backgroundImage: `url('${imageUrl}')`\r\n    };\r\n  }\r\n  // 否则返回空对象\r\n  return {};\r\n});\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    statusBarHeight.value = menuButtonInfo.top;\r\n    navBarHeight.value = menuButtonInfo.height;\r\n    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;\r\n  } catch (e) {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    navBarHeight.value = 44;\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;\r\n  }\r\n};\r\n\r\nconst getCurrentPageUrl = () => {\r\n  try {\r\n    const pages = getCurrentPages();\r\n    const current = pages[pages.length - 1];\r\n    const route = '/' + current.route;\r\n    const options = current.options || {};\r\n    const query = Object.keys(options)\r\n        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options[key])}`)\r\n        .join('&');\r\n    return query ? `${route}?${query}` : route;\r\n  } catch (e) {\r\n    return '/pages_sub/pages_article/detail';\r\n  }\r\n};\r\n\r\nconst ensureLoggedInForAction = () => {\r\n  try {\r\n    const token = uni.getStorageSync('token');\r\n    if (!token) {\r\n      const backUrl = getCurrentPageUrl();\r\n      try { uni.setStorageSync('loginBackPage', backUrl); } catch (e) {}\r\n      uni.navigateTo({ url: '/pages_sub/pages_other/login' });\r\n      return false;\r\n    }\r\n    return true;\r\n  } catch (e) {\r\n    uni.navigateTo({ url: '/pages_sub/pages_other/login' });\r\n    return false;\r\n  }\r\n};\r\n\r\nconst handleOpenCommentInput = () => {\r\n  if (!ensureLoggedInForAction()) return;\r\n  newComment.content = '';\r\n  showNewCommentModal.value = true;\r\n};\r\n\r\nconst closeNewCommentModal = () => {\r\n  showNewCommentModal.value = false;\r\n};\r\n\r\nconst goBack = () => {\r\n  uni.navigateBack({ delta: 1 });\r\n};\r\n\r\nconst article = ref(null);\r\nconst articleId = ref(null);\r\nconst commentList = ref([]);\r\nconst commentTotal = ref(0);\r\nconst newComment = reactive({ content: '', isSubmitting: false });\r\n\r\nconst isCommentsLoading = ref(false);\r\n\r\nconst imageBaseUrl = computed(() => {\r\n  if (!config.imageBaseUrl) return '';\r\n  return config.imageBaseUrl.replace(/\\/$/, '');\r\n});\r\n\r\nconst fetchArticleData = async (id) => {\r\n  loadError.value = false;\r\n  try {\r\n    const response = await getArticleDetail(id);\r\n    if (response.code === 200 && response.data) {\r\n      const rawArticle = response.data;\r\n      let parsedTags = [];\r\n      if (rawArticle.tags && typeof rawArticle.tags === 'string') {\r\n        try {\r\n          parsedTags = JSON.parse(rawArticle.tags);\r\n        } catch (e) {\r\n          console.error('标签解析失败:', e);\r\n        }\r\n      } else if (Array.isArray(rawArticle.tags)) {\r\n        parsedTags = rawArticle.tags;\r\n      }\r\n      if (!Array.isArray(parsedTags)) parsedTags = [];\r\n      article.value = { ...rawArticle, parsedTags };\r\n    } else {\r\n      uni.showToast({ title: response.msg || '获取文章失败', icon: 'none' });\r\n      loadError.value = true;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取文章失败:', error);\r\n    uni.showToast({ title: '网络请求失败', icon: 'error' });\r\n    loadError.value = true;\r\n  }\r\n};\r\n\r\nconst fetchComments = async () => {\r\n  if (!articleId.value) return;\r\n  isCommentsLoading.value = true;\r\n  try {\r\n    const response = await getCommentList({ relatedId: articleId.value, relatedType: 'article' });\r\n    if (response.code === 200 && response.data) {\r\n      commentList.value = response.data.comments || [];\r\n      commentTotal.value = response.data.total || 0;\r\n    } else {\r\n      commentList.value = [];\r\n      commentTotal.value = 0;\r\n    }\r\n  } catch (error) {\r\n    commentList.value = [];\r\n    commentTotal.value = 0;\r\n  } finally {\r\n    isCommentsLoading.value = false;\r\n  }\r\n};\r\n\r\nconst handlePostComment = async ({ content, stateObject }) => {\r\n  if (!content.trim() || stateObject.isSubmitting) return;\r\n  stateObject.isSubmitting = true;\r\n  try {\r\n    const response = await addComment({\r\n      relatedId: articleId.value,\r\n      relatedType: 'article',\r\n      content: content.trim()\r\n    });\r\n    if (response.code === 200) {\r\n      uni.showToast({ title: '评论成功，待审核', icon: 'success' });\r\n      await fetchComments();\r\n      return true;\r\n    } else {\r\n      uni.showToast({ title: response.msg || '评论失败', icon: 'error' });\r\n      return false;\r\n    }\r\n  } catch (error) {\r\n    return false;\r\n  } finally {\r\n    stateObject.isSubmitting = false;\r\n  }\r\n};\r\n\r\nconst postNewComment = async () => {\r\n  if (!ensureLoggedInForAction()) return;\r\n  const success = await handlePostComment({ content: newComment.content, stateObject: newComment });\r\n  if (success) {\r\n    newComment.content = '';\r\n    closeNewCommentModal();\r\n  }\r\n};\r\n\r\n\r\n\r\nconst initPageData = async () => {\r\n  if (!articleId.value) {\r\n    loadError.value = true;\r\n    return;\r\n  }\r\n  await fetchArticleData(articleId.value);\r\n  // 只有在文章数据成功获取后才设置导航栏并获取评论\r\n  if (article.value) {\r\n    await nextTick();\r\n    getNavBarInfo();\r\n    await fetchComments();\r\n  }\r\n};\r\n\r\nconst retryLoad = () => {\r\n  initPageData();\r\n};\r\n\r\nonLoad((options) => {\r\n  articleId.value = options.id;\r\n  initPageData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* [核心修改] 移除了所有 .skeleton- 相关样式，其余样式保持不变 */\r\n.page-container {\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n}\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: #FFFFFF;\r\n}\r\n.scrollable-content {\r\n  height: 100%;\r\n  box-sizing: border-box;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n.error-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n  .empty-icon {\r\n    font-size: 80rpx;\r\n    margin-bottom: 24rpx;\r\n    opacity: 0.5;\r\n  }\r\n  .empty-title {\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    color: #606266;\r\n    margin-bottom: 12rpx;\r\n  }\r\n  .empty-desc {\r\n    font-size: 26rpx;\r\n    color: #909399;\r\n  }\r\n  .retry-btn {\r\n    margin-top: 40rpx;\r\n    padding: 0 60rpx;\r\n    height: 70rpx;\r\n    line-height: 70rpx;\r\n    font-size: 28rpx;\r\n    color: #ffffff;\r\n    background-color: #3c9cff;\r\n    border-radius: 35rpx;\r\n  }\r\n}\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  box-sizing: content-box;\r\n}\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15rpx;\r\n}\r\n.nav-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #000000;\r\n}\r\n.hero-section {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 500rpx;\r\n  .hero-image {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n.main-content {\r\n  padding: 0;\r\n  margin-top: 0;\r\n  position: relative;\r\n  z-index: 3;\r\n}\r\n.article-title {\r\n  font-size: 40rpx;\r\n  color: #23232A;\r\n  font-weight: 700;\r\n  line-height: 1.5;\r\n  padding: 30rpx 30rpx 0 30rpx;\r\n  margin-bottom: 24rpx;\r\n}\r\n.article-meta-new {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n  margin-bottom: 30rpx;\r\n  .meta-left {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 24rpx;\r\n  }\r\n  .meta-text {\r\n    font-size: 24rpx;\r\n    color: #9B9A9A;\r\n  }\r\n}\r\n.summary-card {\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  margin: 0 30rpx 0rpx 30rpx;\r\n  padding: 30rpx;\r\n  .summary-text {\r\n    font-size: 30rpx;\r\n    font-weight: 600;\r\n    color: #495057;\r\n    line-height: 1.8;\r\n  }\r\n}\r\n.content-card {\r\n  background: #FFFFFF;\r\n  border-radius: 24rpx;\r\n  margin: 0 30rpx 0 30rpx;\r\n}\r\n.tags-section-new {\r\n  margin: 0 30rpx 0 30rpx;\r\n  padding: 20rpx 24rpx;\r\n  background: #F2F4FA;\r\n  border-radius: 16rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 8rpx;\r\n  .tags-label {\r\n    font-size: 24rpx;\r\n    color: #66666E;\r\n    font-weight: 600;\r\n    margin-right: 12rpx;\r\n  }\r\n  .tag-item-new {\r\n    font-size: 24rpx;\r\n    color: #66666E;\r\n  }\r\n}\r\n.comment-container {\r\n  margin-top: 20rpx;\r\n  padding: 0 30rpx 40rpx 30rpx;\r\n  background: #ffffff;\r\n}\r\n.comment-header-section {\r\n  padding: 40rpx 0 30rpx 0;\r\n  border-bottom: 2rpx solid #f1f5f9;\r\n  .comment-main-title {\r\n    font-size: 36rpx;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n}\r\n.comment-input-card {\r\n  width: 702rpx;\r\n  height: 72rpx;\r\n  background: #F7F7F7;\r\n  border-radius: 8rpx;\r\n  margin: 30rpx 0;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 24rpx;\r\n  box-sizing: border-box;\r\n  .comment-input-icon {\r\n    width: 32rpx;\r\n    height: 32rpx;\r\n    margin-right: 16rpx;\r\n  }\r\n  .comment-input-placeholder {\r\n    font-size: 28rpx;\r\n    color: #9B9A9A;\r\n  }\r\n}\r\n.comment-list-container {\r\n  padding-top: 20rpx;\r\n}\r\n.empty-state {\r\n  padding: 80rpx 0;\r\n  text-align: center;\r\n  .empty-icon {\r\n    font-size: 80rpx;\r\n    margin-bottom: 24rpx;\r\n    opacity: 0.5;\r\n  }\r\n  .empty-title {\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    color: #606266;\r\n    margin-bottom: 12rpx;\r\n  }\r\n  .empty-desc {\r\n    font-size: 26rpx;\r\n    color: #909399;\r\n  }\r\n}\r\n.comment-popup {\r\n  background: #ffffff;\r\n  .popup-header {\r\n    position: relative;\r\n    padding: 30rpx;\r\n    border-bottom: 2rpx solid #f1f5f9;\r\n    text-align: center;\r\n    .popup-title {\r\n      font-size: 30rpx;\r\n      font-weight: 500;\r\n      color: #606266;\r\n    }\r\n    .popup-close {\r\n      position: absolute;\r\n      right: 30rpx;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      font-size: 40rpx;\r\n      color: #909399;\r\n    }\r\n  }\r\n  .popup-body {\r\n    padding: 30rpx;\r\n    .comment-input {\r\n      width: 100%;\r\n      min-height: 200rpx;\r\n      background: #f8f9fa;\r\n      border: 2rpx solid #e5e7eb;\r\n      border-radius: 16rpx;\r\n      padding: 24rpx;\r\n      font-size: 28rpx;\r\n      line-height: 1.6;\r\n      color: #303133;\r\n    }\r\n    .popup-footer {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-top: 20rpx;\r\n      .comment-counter {\r\n        font-size: 24rpx;\r\n        color: #909399;\r\n      }\r\n      .comment-submit {\r\n        padding: 16rpx 40rpx;\r\n        border-radius: 30rpx;\r\n        background: #dcdfe6;\r\n        color: #ffffff;\r\n        font-size: 28rpx;\r\n        &.comment-submit-active {\r\n          background: #023F98;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_article/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "computed", "reactive", "config", "getArticleDetail", "getCommentList", "addComment", "nextTick", "onLoad"], "mappings": ";;;;;;;;;;;;;;;;;;AAwHA,MAAM,SAAS,MAAW;AAO1B,MAAM,cAAc,MAAW;AAG/B,MAAM,yBAAyB;;;;AAC/B,UAAM,wBAAwBA,cAAG,MAAC,OAAO,sBAAsB;AAC/D,UAAM,kBAAkBC,cAAAA,IAAI,CAAC;AAC7B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,sBAAsBA,cAAAA,IAAI,KAAK;AACrC,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,SAASA,cAAG,IAACD,cAAG,MAAC,eAAe,cAAc,KAAK,CAAA,CAAE;AAE3D,UAAM,sBAAsBE,cAAQ,SAAC,MAAM;AAEzC,aAAO,OAAO,MAAM,8BAA8B;AAAA,IACpD,CAAC;AAGD,UAAM,mBAAmBA,cAAQ,SAAC,MAAM;AAEtC,YAAM,WAAW,OAAO,MAAM;AAC9B,UAAI,UAAU;AAEZ,eAAO;AAAA,UACL,iBAAiB,QAAQ,QAAQ;AAAA,QACvC;AAAA,MACG;AAED,aAAO;IACT,CAAC;AAED,UAAM,gBAAgB,MAAM;AAC1B,UAAI;AACF,cAAM,iBAAiBF,oBAAI;AAC3B,wBAAgB,QAAQ,eAAe;AACvC,qBAAa,QAAQ,eAAe;AACpC,qBAAa,QAAQ,eAAe,SAAS;AAAA,MAC9C,SAAQ,GAAG;AACV,cAAM,aAAaA,oBAAI;AACvB,wBAAgB,QAAQ,WAAW,mBAAmB;AACtD,qBAAa,QAAQ;AACrB,qBAAa,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ;AAAA,MACnE;AAAA,IACH;AAEA,UAAM,oBAAoB,MAAM;AAC9B,UAAI;AACF,cAAM,QAAQ;AACd,cAAM,UAAU,MAAM,MAAM,SAAS,CAAC;AACtC,cAAM,QAAQ,MAAM,QAAQ;AAC5B,cAAM,UAAU,QAAQ,WAAW;AACnC,cAAM,QAAQ,OAAO,KAAK,OAAO,EAC5B,IAAI,SAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,QAAQ,GAAG,CAAC,CAAC,EAAE,EAC3E,KAAK,GAAG;AACb,eAAO,QAAQ,GAAG,KAAK,IAAI,KAAK,KAAK;AAAA,MACtC,SAAQ,GAAG;AACV,eAAO;AAAA,MACR;AAAA,IACH;AAEA,UAAM,0BAA0B,MAAM;AACpC,UAAI;AACF,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAI,CAAC,OAAO;AACV,gBAAM,UAAU;AAChB,cAAI;AAAEA,0BAAAA,MAAI,eAAe,iBAAiB,OAAO;AAAA,UAAI,SAAQ,GAAG;AAAA,UAAE;AAClEA,wBAAAA,MAAI,WAAW,EAAE,KAAK,+BAAgC,CAAA;AACtD,iBAAO;AAAA,QACR;AACD,eAAO;AAAA,MACR,SAAQ,GAAG;AACVA,sBAAAA,MAAI,WAAW,EAAE,KAAK,+BAAgC,CAAA;AACtD,eAAO;AAAA,MACR;AAAA,IACH;AAEA,UAAM,yBAAyB,MAAM;AACnC,UAAI,CAAC,wBAAuB;AAAI;AAChC,iBAAW,UAAU;AACrB,0BAAoB,QAAQ;AAAA,IAC9B;AAEA,UAAM,uBAAuB,MAAM;AACjC,0BAAoB,QAAQ;AAAA,IAC9B;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa,EAAE,OAAO,EAAG,CAAA;AAAA,IAC/B;AAEA,UAAM,UAAUC,cAAAA,IAAI,IAAI;AACxB,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,aAAaE,cAAQ,SAAC,EAAE,SAAS,IAAI,cAAc,MAAK,CAAE;AAEhE,UAAM,oBAAoBF,cAAAA,IAAI,KAAK;AAEnC,UAAM,eAAeC,cAAQ,SAAC,MAAM;AAClC,UAAI,CAACE,aAAM,OAAC;AAAc,eAAO;AACjC,aAAOA,aAAM,OAAC,aAAa,QAAQ,OAAO,EAAE;AAAA,IAC9C,CAAC;AAED,UAAM,mBAAmB,OAAO,OAAO;AACrC,gBAAU,QAAQ;AAClB,UAAI;AACF,cAAM,WAAW,MAAMC,qCAAiB,EAAE;AAC1C,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC1C,gBAAM,aAAa,SAAS;AAC5B,cAAI,aAAa,CAAA;AACjB,cAAI,WAAW,QAAQ,OAAO,WAAW,SAAS,UAAU;AAC1D,gBAAI;AACF,2BAAa,KAAK,MAAM,WAAW,IAAI;AAAA,YACxC,SAAQ,GAAG;AACVL,4BAAA,MAAA,MAAA,SAAA,6CAAc,WAAW,CAAC;AAAA,YAC3B;AAAA,UACF,WAAU,MAAM,QAAQ,WAAW,IAAI,GAAG;AACzC,yBAAa,WAAW;AAAA,UACzB;AACD,cAAI,CAAC,MAAM,QAAQ,UAAU;AAAG,yBAAa,CAAA;AAC7C,kBAAQ,QAAQ,EAAE,GAAG,YAAY,WAAU;AAAA,QACjD,OAAW;AACLA,8BAAI,UAAU,EAAE,OAAO,SAAS,OAAO,UAAU,MAAM,OAAM,CAAE;AAC/D,oBAAU,QAAQ;AAAA,QACnB;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,6CAAc,WAAW,KAAK;AAC9BA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,QAAO,CAAE;AAChD,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAEA,UAAM,gBAAgB,YAAY;AAChC,UAAI,CAAC,UAAU;AAAO;AACtB,wBAAkB,QAAQ;AAC1B,UAAI;AACF,cAAM,WAAW,MAAMM,4CAAc,eAAC,EAAE,WAAW,UAAU,OAAO,aAAa,UAAS,CAAE;AAC5F,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC1C,sBAAY,QAAQ,SAAS,KAAK,YAAY,CAAA;AAC9C,uBAAa,QAAQ,SAAS,KAAK,SAAS;AAAA,QAClD,OAAW;AACL,sBAAY,QAAQ;AACpB,uBAAa,QAAQ;AAAA,QACtB;AAAA,MACF,SAAQ,OAAO;AACd,oBAAY,QAAQ;AACpB,qBAAa,QAAQ;AAAA,MACzB,UAAY;AACR,0BAAkB,QAAQ;AAAA,MAC3B;AAAA,IACH;AAEA,UAAM,oBAAoB,OAAO,EAAE,SAAS,kBAAkB;AAC5D,UAAI,CAAC,QAAQ,KAAM,KAAI,YAAY;AAAc;AACjD,kBAAY,eAAe;AAC3B,UAAI;AACF,cAAM,WAAW,MAAMC,uDAAW;AAAA,UAChC,WAAW,UAAU;AAAA,UACrB,aAAa;AAAA,UACb,SAAS,QAAQ,KAAM;AAAA,QAC7B,CAAK;AACD,YAAI,SAAS,SAAS,KAAK;AACzBP,wBAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,UAAS,CAAE;AACpD,gBAAM,cAAa;AACnB,iBAAO;AAAA,QACb,OAAW;AACLA,8BAAI,UAAU,EAAE,OAAO,SAAS,OAAO,QAAQ,MAAM,QAAO,CAAE;AAC9D,iBAAO;AAAA,QACR;AAAA,MACF,SAAQ,OAAO;AACd,eAAO;AAAA,MACX,UAAY;AACR,oBAAY,eAAe;AAAA,MAC5B;AAAA,IACH;AAEA,UAAM,iBAAiB,YAAY;AACjC,UAAI,CAAC,wBAAuB;AAAI;AAChC,YAAM,UAAU,MAAM,kBAAkB,EAAE,SAAS,WAAW,SAAS,aAAa,WAAU,CAAE;AAChG,UAAI,SAAS;AACX,mBAAW,UAAU;AACrB;MACD;AAAA,IACH;AAIA,UAAM,eAAe,YAAY;AAC/B,UAAI,CAAC,UAAU,OAAO;AACpB,kBAAU,QAAQ;AAClB;AAAA,MACD;AACD,YAAM,iBAAiB,UAAU,KAAK;AAEtC,UAAI,QAAQ,OAAO;AACjB,cAAMQ,cAAQ,WAAA;AACd;AACA,cAAM,cAAa;AAAA,MACpB;AAAA,IACH;AAEA,UAAM,YAAY,MAAM;AACtB;IACF;AAEAC,kBAAM,OAAC,CAAC,YAAY;AAClB,gBAAU,QAAQ,QAAQ;AAC1B;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9UD,GAAG,WAAW,eAAe;"}