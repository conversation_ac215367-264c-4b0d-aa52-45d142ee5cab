<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.platform.mapper.HongdaAdMapper">

    <resultMap type="com.hongda.platform.domain.HongdaAd" id="HongdaAdResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="positionCode" column="position_code"/>
        <result property="imageUrl" column="image_url"/>
        <result property="linkType" column="link_type"/>
        <result property="linkTarget" column="link_target"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="linkTargetTitle" column="link_target_title"/>
    </resultMap>

    <sql id="selectHongdaAdVo">
        select id,
               title,
               position_code,
               image_url,
               link_type,
               link_target,
               start_time,
               end_time,
               sort_order,
               status,
               create_by,
               create_time,
               update_by,
               update_time
        from hongda_ad
    </sql>

    <select id="selectHongdaAdList" parameterType="com.hongda.platform.domain.HongdaAd" resultMap="HongdaAdResult">
        SELECT
        a.id,
        a.title,
        a.position_code,
        a.image_url,
        a.link_type,
        a.link_target,
        a.sort_order,
        a.status,
        a.create_time,
        -- [核心修正] 使用 CASE 语句并根据正确的字段名从关联表中获取标题/名称
        CASE
        WHEN a.link_type = 'ARTICLE' THEN art.title
        WHEN a.link_type = 'EVENT'   THEN eve.title
        WHEN a.link_type = 'COUNTRY' THEN cou.name_cn   -- [修正] 此处从 name 修改为 name_cn
        WHEN a.link_type = 'PARK'    THEN par.name
        ELSE NULL
        END AS link_target_title
        FROM
        hongda_ad a
        LEFT JOIN hongda_article art ON a.link_type = 'ARTICLE' AND a.link_target = CAST(art.id AS CHAR)
        LEFT JOIN hongda_event eve   ON a.link_type = 'EVENT'   AND a.link_target = CAST(eve.id AS CHAR)
        LEFT JOIN hongda_country cou ON a.link_type = 'COUNTRY' AND a.link_target = CAST(cou.id AS CHAR)
        LEFT JOIN hongda_park par    ON a.link_type = 'PARK'    AND a.link_target = CAST(par.id AS CHAR)
        <where>
            <if test="title != null and title != ''">and a.title like concat('%', #{title}, '%')</if>
            <if test="positionCode != null and positionCode != ''">and a.position_code = #{positionCode}</if>
            <if test="status != null">and a.status = #{status}</if>
        </where>
    </select>

    <select id="selectHongdaAdById" parameterType="Long" resultMap="HongdaAdResult">
        <include refid="selectHongdaAdVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaAd" parameterType="com.hongda.platform.domain.HongdaAd" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_ad
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="positionCode != null and positionCode != ''">position_code,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="linkType != null and linkType != ''">link_type,</if>
            <if test="linkTarget != null">link_target,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="positionCode != null and positionCode != ''">#{positionCode},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="linkType != null and linkType != ''">#{linkType},</if>
            <if test="linkTarget != null">#{linkTarget},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateHongdaAd" parameterType="com.hongda.platform.domain.HongdaAd">
        update hongda_ad
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="positionCode != null and positionCode != ''">position_code = #{positionCode},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="linkType != null">link_type = #{linkType},</if>
            <if test="linkTarget != null">link_target = #{linkTarget},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaAdById" parameterType="Long">
        delete from hongda_ad where id = #{id}
    </delete>

    <delete id="deleteHongdaAdByIds" parameterType="String">
        delete from hongda_ad where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>