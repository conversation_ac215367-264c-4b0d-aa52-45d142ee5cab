<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="关联类型" prop="relatedType">
        <el-select v-model="queryParams.relatedType" placeholder="请选择关联类型" clearable style="width: 200px">
          <el-option
              v-for="dict in hongda_comment_related_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="用户" prop="userId">
        <el-select
            v-model="queryParams.userId"
            placeholder="请输入用户昵称搜索"
            filterable
            remote
            clearable
            :remote-method="remoteUserSearch"
            :loading="userLoading"
            style="width: 220px"
        >
          <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="关联内容" prop="relatedTitle">
        <el-input
            v-model="queryParams.relatedTitle"
            placeholder="请输入关联内容的标题"
            clearable
            style="width: 220px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
          <el-option
              v-for="dict in hongda_comment_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['data:comment:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete()"
            v-hasPermi="['data:comment:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="commentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="用户" align="center" prop="nickname" min-width="150" />
      <el-table-column label="评论内容" prop="content" :show-overflow-tooltip="true" min-width="250" />
      <el-table-column label="关联内容" min-width="200">
        <template #default="scope">
          <div>
            <dict-tag :options="hongda_comment_related_type" :value="scope.row.relatedType" />
          </div>
          <div class="related-title">{{ scope.row.relatedTitle }}</div>
        </template>
      </el-table-column>
      <el-table-column label="发表时间" align="center" prop="createTime" width="160" />
      <el-table-column label="状态" align="center" width="100">
        <template #default="scope">
          <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="commentRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="发表用户" prop="userId">
          <el-select
              v-model="form.userId"
              placeholder="请输入用户昵称进行搜索"
              filterable
              remote
              clearable
              :remote-method="remoteDialogUserSearch"
              :loading="dialogUserLoading"
              style="width: 100%"
          >
            <el-option
                v-for="item in dialogUserList"
                :key="item.id"
                :label="item.nickname + ' (ID:' + item.id + ')'"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联类型" prop="relatedType">
          <el-select v-model="form.relatedType" placeholder="请选择关联类型">
            <el-option
                v-for="dict in hongda_comment_related_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联的内容ID" prop="relatedId">
          <el-input v-model="form.relatedId" placeholder="请输入关联的内容ID" />
        </el-form-item>
        <el-form-item label="评论内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入内容" :rows="4" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Comment">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue';
import { listComment, getComment, delComment, addComment, updateComment } from "@/api/data/comment";
import { listMiniuser } from "@/api/data/miniuser.js";

const { proxy } = getCurrentInstance();
const { hongda_comment_related_type, hongda_comment_status } = proxy.useDict('hongda_comment_related_type', 'hongda_comment_status');

// --- 响应式状态定义 ---
const commentList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true); // 是否禁用单个修改
const multiple = ref(true); // 是否禁用多个删除
const total = ref(0);
const title = ref("");

// 用户搜索相关状态
const userList = ref([]);
const userLoading = ref(false);
const dialogUserList = ref([]);
const dialogUserLoading = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: null,
    relatedType: null,
    status: null,
    relatedTitle: null
  },
  rules: {
    userId: [{ required: true, message: "发表用户不能为空", trigger: "change" }],
    relatedType: [{ required: true, message: "关联类型不能为空", trigger: "change" }],
    relatedId: [{ required: true, message: "关联的内容ID不能为空", trigger: "blur" }],
    content: [{ required: true, message: "评论内容不能为空", trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// --- 核心业务方法 ---

/** 查询评论列表 */
function getList() {
  loading.value = true;
  listComment(queryParams.value).then(response => {
    commentList.value = response.data;
    total.value = response.total;
    loading.value = false;
  });
}

/** 远程搜索用户（弹窗内） */
const remoteDialogUserSearch = (query) => {
  if (query) {
    dialogUserLoading.value = true;
    listMiniuser({ nickname: query, pageSize: 50 }).then(response => {
      dialogUserList.value = response.rows;
      dialogUserLoading.value = false;
    });
  } else {
    dialogUserList.value = [];
  }
};

/** 远程搜索用户（搜索栏） */
const remoteUserSearch = (query) => {
  if (query) {
    userLoading.value = true;
    listMiniuser({ nickname: query, pageSize: 50 }).then(response => {
      userList.value = response.rows;
      userLoading.value = false;
    });
  } else {
    userList.value = [];
  }
};

/** 表单重置 */
function reset() {
  form.value = {
    id: null,
    userId: null,
    relatedType: null,
    relatedId: null,
    content: null,
    status: 1,
  };
  dialogUserList.value = [];
  proxy.resetForm("commentRef");
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加评论";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value[0];
  getComment(_id).then(response => {
    form.value = response.data;
    // 为了在弹窗中能回显用户名，需要手动查询一次该用户信息并放入列表
    if (form.value.userId) {
      listMiniuser({ id: form.value.userId }).then(userResponse => {
        dialogUserList.value = userResponse.rows;
      });
    }
    open.value = true;
    title.value = "修改评论";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["commentRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateComment(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addComment(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id ? [row.id] : ids.value;
  proxy.$modal.confirm('是否确认删除评论编号为"' + _ids.join(",") + '"的数据项？').then(function() {
    return delComment(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 处理评论状态修改 */
function handleStatusChange(row) {
  const text = row.status === 1 ? "显示" : "隐藏";
  proxy.$modal.confirm(`确认要将这条评论设置为"${text}"状态吗？`).then(function() {
    return updateComment({ id: row.id, status: row.status });
  }).then(() => {
    proxy.$modal.msgSuccess(text + "设置成功");
  }).catch(function() {
    // 操作失败或取消时，恢复开关状态
    row.status = row.status === 1 ? 0 : 1;
  });
}

// --- 生命周期钩子 ---
onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.user-info {
  display: flex;
  align-items: center;
  gap: 10px; /* 控制头像和昵称的间距 */
  .nickname {
    font-weight: 500;
  }
}
.related-title {
  color: #606266;
  font-size: 13px;
  margin-top: 5px;
}
</style>