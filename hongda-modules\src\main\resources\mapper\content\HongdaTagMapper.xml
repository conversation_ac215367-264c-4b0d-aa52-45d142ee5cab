<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaTagMapper">
    
    <resultMap type="HongdaTag" id="HongdaTagResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="sortOrder"    column="sort_order"    />
    </resultMap>

    <sql id="selectHongdaTagVo">
        select id, name, sort_order from hongda_tag
    </sql>

    <select id="selectHongdaTagList" parameterType="HongdaTag" resultMap="HongdaTagResult">
        <include refid="selectHongdaTagVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>
    
    <select id="selectHongdaTagById" parameterType="Long" resultMap="HongdaTagResult">
        <include refid="selectHongdaTagVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaTag" parameterType="HongdaTag" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="sortOrder != null">sort_order,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
         </trim>
    </insert>

    <update id="updateHongdaTag" parameterType="HongdaTag">
        update hongda_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaTagById" parameterType="Long">
        delete from hongda_tag where id = #{id}
    </delete>

    <delete id="deleteHongdaTagByIds" parameterType="String">
        delete from hongda_tag where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countArticlesByTagId" parameterType="Long" resultType="Integer">
        select count(*) from hongda_article_tag_relation where tag_id = #{tagId}
    </select>
</mapper>