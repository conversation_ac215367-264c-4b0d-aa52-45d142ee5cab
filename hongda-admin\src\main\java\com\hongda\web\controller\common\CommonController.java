package com.hongda.web.controller.common;

import com.hongda.common.config.RuoYiConfig;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.service.OssFileStorageService;
import com.hongda.common.utils.ServletUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.common.utils.file.FileUtils;
import com.hongda.common.utils.uuid.IdUtils;
import com.hongda.framework.config.ServerConfig;
import com.hongda.framework.web.service.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private OssFileStorageService ossFileStorageService;

    // [🔥 核心修改] 移除不再需要的 @Value 注入
    // @Value("${hongda.oss.url-expire-minutes}")
    // private int urlExpireMinutes;

    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        HttpServletRequest request = ServletUtils.getRequest();
        Long wxUserId = tokenService.getWxUserId(request);

        if (wxUserId == null) {
            if (tokenService.getLoginUser(request) == null) {
                return AjaxResult.error(401, "请求访问：/common/upload，认证失败，无法访问系统资源");
            }
        }

        try {
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String objectName = IdUtils.fastUUID() + fileExtension;

            try (InputStream inputStream = file.getInputStream()) {
                ossFileStorageService.upload(objectName, inputStream);
            }

            // [🔥 核心修改] 调用新的方法获取永久URL
            String publicUrl = ossFileStorageService.getPublicUrl(objectName);

            AjaxResult ajax = AjaxResult.success();
            ajax.put("objectName", objectName);
            ajax.put("url", publicUrl); // 返回永久URL
            ajax.put("originalFilename", originalFilename);

            return ajax;
        } catch (Exception e) {
            log.error("上传文件到OSS失败", e);
            return AjaxResult.error("上传失败：" + e.getMessage());
        }
    }

    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        HttpServletRequest request = ServletUtils.getRequest();
        Long wxUserId = tokenService.getWxUserId(request);

        if (wxUserId == null) {
            if (tokenService.getLoginUser(request) == null) {
                return AjaxResult.error(401, "请求访问：/common/uploads，认证失败，无法访问系统资源");
            }
        }

        try
        {
            List<Map<String, String>> successList = new ArrayList<>();

            for (MultipartFile file : files)
            {
                String originalFilename = file.getOriginalFilename();
                String fileExtension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                }
                String objectName = IdUtils.fastUUID() + fileExtension;

                ossFileStorageService.upload(objectName, file.getInputStream());

                // [🔥 核心修改] 调用新的方法获取永久URL
                String publicUrl = ossFileStorageService.getPublicUrl(objectName);

                Map<String, String> fileResult = new HashMap<>();
                fileResult.put("objectName", objectName);
                fileResult.put("url", publicUrl); // 返回永久URL
                fileResult.put("originalFilename", originalFilename);
                successList.add(fileResult);
            }

            AjaxResult ajax = AjaxResult.success("批量上传成功");
            ajax.put("files", successList);
            return ajax;
        }
        catch (Exception e)
        {
            log.error("批量上传文件到OSS失败", e);
            return AjaxResult.error("批量上传失败：" + e.getMessage());
        }
    }

    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            String localPath = RuoYiConfig.getProfile();
            String downloadPath = localPath + FileUtils.stripPrefix(resource);
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }
}