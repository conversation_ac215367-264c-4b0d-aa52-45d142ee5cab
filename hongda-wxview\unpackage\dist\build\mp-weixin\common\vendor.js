"use strict";
/**
* @vue/shared v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/
function e(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const t={},n=[],o=()=>{},r=()=>!1,s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),i=e=>e.startsWith("onUpdate:"),c=Object.assign,a=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},l=Object.prototype.hasOwnProperty,u=(e,t)=>l.call(e,t),f=Array.isArray,p=e=>"[object Map]"===x(e),d=e=>"[object Set]"===x(e),h=e=>"function"==typeof e,g=e=>"string"==typeof e,m=e=>"symbol"==typeof e,v=e=>null!==e&&"object"==typeof e,_=e=>(v(e)||h(e))&&h(e.then)&&h(e.catch),y=Object.prototype.toString,x=e=>y.call(e),w=e=>"[object Object]"===x(e),b=e=>g(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,$=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),k=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},S=/-(\w)/g,O=k((e=>e.replace(S,((e,t)=>t?t.toUpperCase():"")))),P=/\B([A-Z])/g,C=k((e=>e.replace(P,"-$1").toLowerCase())),E=k((e=>e.charAt(0).toUpperCase()+e.slice(1))),I=k((e=>e?`on${E(e)}`:"")),A=(e,t)=>!Object.is(e,t),j=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},L=e=>{const t=parseFloat(e);return isNaN(t)?e:t};function R(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=g(o)?D(o):R(o);if(r)for(const e in r)t[e]=r[e]}return t}if(g(e)||v(e))return e}const M=/;(?![^(]*\))/g,T=/:([^]+)/,V=/\/\*[^]*?\*\//g;function D(e){const t={};return e.replace(V,"").split(M).forEach((e=>{if(e){const n=e.split(T);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function H(e){let t="";if(g(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const o=H(e[n]);o&&(t+=o+" ")}else if(v(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const N=(e,t)=>t&&t.__v_isRef?N(e,t.value):p(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[B(t,o)+" =>"]=n,e)),{})}:d(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>B(e)))}:m(t)?B(t):!v(t)||f(t)||w(t)?t:String(t),B=(e,t="")=>{var n;return m(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};function U(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function W(e,t){if(!g(t))return;const n=(t=t.replace(/\[(\d+)\]/g,".$1")).split(".");let o=n[0];return e||(e={}),1===n.length?e[o]:W(e[o],n.slice(1).join("."))}function z(e){let t={};return w(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}const F=/:/g;const K=encodeURIComponent;function q(e,t=K){const n=e?Object.keys(e).map((n=>{let o=e[n];return void 0===typeof o||null===o?o="":w(o)&&(o=JSON.stringify(o)),t(n)+"="+t(o)})).filter((e=>e.length>0)).join("&"):null;return n?`?${n}`:""}const G=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const J=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"],Z=(()=>({onPageScroll:1,onShareAppMessage:2,onShareTimeline:4}))();function Q(e,t,n=!0){return!(n&&!h(t))&&(J.indexOf(e)>-1||0===e.indexOf("on"))}let X;const Y=[];const ee=U(((e,t)=>t(e))),te=function(){};te.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var s=o.length-1;s>=0;s--)if(o[s].fn===t||o[s].fn._===t||o[s]._id===t){o.splice(s,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var ne=te;const oe=["{","}"];const re=/^(?:\d)+/,se=/^(?:\w)+/;const ie=Object.prototype.hasOwnProperty,ce=(e,t)=>ie.call(e,t),ae=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=oe){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,s="";for(;r<e.length;){let i=e[r++];if(i===t){s&&o.push({type:"text",value:s}),s="";let t="";for(i=e[r++];void 0!==i&&i!==n;)t+=i,i=e[r++];const c=i===n,a=re.test(t)?"list":c&&se.test(t)?"named":"unknown";o.push({value:t,type:a})}else s+=i}return s&&o.push({type:"text",value:s}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(s=t,null!==s&&"object"==typeof s?"named":"unknown");var s;if("unknown"===r)return n;for(;o<e.length;){const s=e[o];switch(s.type){case"text":n.push(s.value);break;case"list":n.push(t[parseInt(s.value,10)]);break;case"named":"named"===r&&n.push(t[s.value])}o++}return n}(o,t)}};function le(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class ue{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||ae,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=le(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{ce(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=le(t,this.messages))&&(o=this.messages[t]):n=t,ce(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function fe(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let pe=1;const de={};function he(e,t,n){if("number"==typeof e){const o=de[e];if(o)return o.keepAlive||delete de[e],o.callback(t,n)}return t}const ge="success",me="fail",ve="complete";function _e(e,t={},{beforeAll:n,beforeSuccess:o}={}){w(t)||(t={});const{success:r,fail:s,complete:i}=function(e){const t={};for(const n in e){const o=e[n];h(o)&&(t[n]=fe(o),delete e[n])}return t}(t),c=h(r),a=h(s),l=h(i),u=pe++;return function(e,t,n,o=!1){de[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),h(n)&&n(u),u.errMsg===e+":ok"?(h(o)&&o(u,t),c&&r(u)):a&&s(u),l&&i(u)})),u}const ye="success",xe="fail",we="complete",be={},$e={};function ke(e,t){return function(n){return e(n,t)||n}}function Se(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const s=e[r];if(o)o=Promise.resolve(ke(s,n));else{const e=s(t,n);if(_(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Oe(e,t={}){return[ye,xe,we].forEach((n=>{const o=e[n];if(!f(o))return;const r=t[n];t[n]=function(e){Se(o,e,t).then((e=>h(r)&&r(e)||e))}})),t}function Pe(e,t){const n=[];f(be.returnValue)&&n.push(...be.returnValue);const o=$e[e];return o&&f(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Ce(e){const t=Object.create(null);Object.keys(be).forEach((e=>{"returnValue"!==e&&(t[e]=be[e].slice())}));const n=$e[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Ee(e,t,n,o){const r=Ce(e);if(r&&Object.keys(r).length){if(f(r.invoke)){return Se(r.invoke,n).then((n=>t(Oe(Ce(e),n),...o)))}return t(Oe(r,n),...o)}return t(n,...o)}function Ie(e,t){return(n={},...o)=>function(e){return!(!w(e)||![ge,me,ve].find((t=>h(e[t]))))}(n)?Pe(e,Ee(e,t,c({},n),o)):Pe(e,new Promise(((r,s)=>{Ee(e,t,c({},n,{success:r,fail:s}),o)})))}function Ae(e,t,n,o={}){const r=t+":fail";let s="";return s=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,he(e,c({errMsg:s},o))}function je(e,t,n,o){const r=function(e,t){e[0]}(t);if(r)return r}function Le(e,t,n,o){return n=>{const r=_e(e,n,o),s=je(0,[n]);return s?Ae(r,e,s):t(n,{resolve:t=>function(e,t,n){return he(e,c(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Ae(r,e,function(e){return!e||g(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Re(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=je(0,e);if(n)throw new Error(n);return t.apply(null,e)}}(0,t)}let Me=!1,Te=0,Ve=0;const De=Re(0,((e,t)=>{if(0===Te&&function(){var e,t;let n,o,r;{const s=(null===(e=wx.getWindowInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync(),i=(null===(t=wx.getDeviceInfo)||void 0===t?void 0:t.call(wx))||wx.getSystemInfoSync();n=s.windowWidth,o=s.pixelRatio,r=i.platform}Te=n,Ve=o,Me="ios"===r}(),0===(e=Number(e)))return 0;let n=e/750*(t||Te);return n<0&&(n=-n),n=Math.floor(n+1e-4),0===n&&(n=1!==Ve&&Me?.5:1),e<0?-n:n}));function He(e,t){Object.keys(t).forEach((n=>{h(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):f(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function Ne(e,t){e&&t&&Object.keys(t).forEach((n=>{const o=e[n],r=t[n];f(o)&&h(r)&&a(o,r)}))}const Be=Re(0,((e,t)=>{g(e)&&w(t)?He($e[e]||($e[e]={}),t):w(e)&&He(be,e)})),Ue=Re(0,((e,t)=>{g(e)?w(t)?Ne($e[e],t):delete $e[e]:w(e)&&Ne(be,e)}));const We=new class{constructor(){this.$emitter=new ne}on(e,t){return this.$emitter.on(e,t)}once(e,t){return this.$emitter.once(e,t)}off(e,t){e?this.$emitter.off(e,t):this.$emitter.e={}}emit(e,...t){this.$emitter.emit(e,...t)}},ze=Re(0,((e,t)=>(We.on(e,t),()=>We.off(e,t)))),Fe=Re(0,((e,t)=>(We.once(e,t),()=>We.off(e,t)))),Ke=Re(0,((e,t)=>{f(e)||(e=e?[e]:[]),e.forEach((e=>{We.off(e,t)}))})),qe=Re(0,((e,...t)=>{We.emit(e,...t)}));let Ge,Je,Ze;function Qe(e){try{return JSON.parse(e)}catch(t){}return e}const Xe=[];function Ye(e,t){Xe.forEach((n=>{n(e,t)})),Xe.length=0}const et=Ie(tt="getPushClientId",function(e,t,n,o){return Le(e,t,0,o)}(tt,((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{void 0===Ze&&(Ze=!1,Ge="",Je="uniPush is not enabled"),Xe.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==Ge&&Ye(Ge,Je)}))}),0,nt));var tt,nt;const ot=[],rt=/^\$|__f__|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|rpx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/,st=/^create|Manager$/,it=["createBLEConnection"],ct=["request","downloadFile","uploadFile","connectSocket"],at=["createBLEConnection"],lt=/^on|^off/;function ut(e){return st.test(e)&&-1===it.indexOf(e)}function ft(e){return rt.test(e)&&-1===at.indexOf(e)}function pt(e){return-1!==ct.indexOf(e)}function dt(e){return!(ut(e)||ft(e)||function(e){return lt.test(e)&&"onPush"!==e}(e))}function ht(e,t){return dt(e)&&h(t)?function(n={},...o){return h(n.success)||h(n.fail)||h(n.complete)?Pe(e,Ee(e,t,c({},n),o)):Pe(e,new Promise(((r,s)=>{Ee(e,t,c({},n,{success:r,fail:s}),o)})))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e&&e()).then((()=>n))),(n=>t.resolve(e&&e()).then((()=>{throw n}))))});const gt=["success","fail","cancel","complete"];const mt=()=>{const e=h(getApp)&&getApp({allowDefault:!0});return e&&e.$vm?e.$vm.$locale:function(){var e;let t="";{const n=(null===(e=wx.getAppBaseInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync();t=le(n&&n.language?n.language:"en")||"en"}return t}()},vt=[];"undefined"!=typeof global&&(global.getLocale=mt);let _t;function yt(e=wx){return function(t,n){_t=_t||e.getStorageSync("__DC_STAT_UUID"),_t||(_t=Date.now()+""+Math.floor(1e7*Math.random()),wx.setStorage({key:"__DC_STAT_UUID",data:_t})),n.deviceId=_t}}function xt(e,t){if(e.safeArea){const n=e.safeArea;t.safeAreaInsets={top:n.top,left:n.left,right:e.windowWidth-n.right,bottom:e.screenHeight-n.bottom}}}function wt(e,t){let n="",o="";switch(n=e.split(" ")[0]||t,o=e.split(" ")[1]||"",n=n.toLowerCase(),n){case"harmony":case"ohos":case"openharmony":n="harmonyos";break;case"iphone os":n="ios";break;case"mac":case"darwin":n="macos";break;case"windows_nt":n="windows"}return{osName:n,osVersion:o}}function bt(e,t){let n=e.deviceType||"phone";{const e={ipad:"pad",windows:"pc",mac:"pc"},o=Object.keys(e),r=t.toLowerCase();for(let t=0;t<o.length;t++){const s=o[t];if(-1!==r.indexOf(s)){n=e[s];break}}}return n}function $t(e){let t=e;return t&&(t=t.toLowerCase()),t}function kt(e){return mt?mt():e}function St(e){let t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}const Ot={returnValue:(e,t)=>{xt(e,t),yt()(e,t),function(e,t){const{brand:n="",model:o="",system:r="",language:s="",theme:i,version:a,platform:l,fontSizeSetting:u,SDKVersion:f,pixelRatio:p,deviceOrientation:d}=e,{osName:h,osVersion:g}=wt(r,l);let m=a,v=bt(e,o),_=$t(n),y=St(e),x=d,w=p,b=f;const $=(s||"").replace(/_/g,"-"),k={appId:"__UNI__67A511F",appName:"hongda-wxview",appVersion:"1.0.0",appVersionCode:"100",appLanguage:kt($),uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75",uniPlatform:"mp-weixin",deviceBrand:_,deviceModel:o,deviceType:v,devicePixelRatio:w,deviceOrientation:x,osName:h,osVersion:g,hostTheme:i,hostVersion:m,hostLanguage:$,hostName:y,hostSDKVersion:b,hostFontSizeSetting:u,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};c(t,k)}(e,t)}},Pt=Ot,Ct={args(e,t){let n=parseInt(e.current);if(isNaN(n))return;const o=e.urls;if(!f(o))return;const r=o.length;return r?(n<0?n=0:n>=r&&(n=r-1),n>0?(t.current=o[n],t.urls=o.filter(((e,t)=>!(t<n)||e!==o[n]))):t.current=o[0],{indicator:!1,loop:!1}):void 0}},Et={args(e,t){t.alertText=e.title}},It={returnValue:(e,t)=>{const{brand:n,model:o,system:r="",platform:s=""}=e;let i=bt(e,o),a=$t(n);yt()(e,t);const{osName:l,osVersion:u}=wt(r,s);t=z(c(t,{deviceType:i,deviceBrand:a,deviceModel:o,osName:l,osVersion:u}))}},At={returnValue:(e,t)=>{const{version:n,language:o,SDKVersion:r,theme:s}=e;let i=St(e),a=(o||"").replace(/_/g,"-");const l={hostVersion:n,hostLanguage:a,hostName:i,hostSDKVersion:r,hostTheme:s,appId:"__UNI__67A511F",appName:"hongda-wxview",appVersion:"1.0.0",appVersionCode:"100",appLanguage:kt(a),isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75"};c(t,l)}},jt={returnValue:(e,t)=>{xt(e,t),t=z(c(t,{windowTop:0,windowBottom:0}))}},Lt={args(e){const t=getApp({allowDefault:!0})||{};t.$vm?gr("onError",e,t.$vm.$):(wx.$onErrorHandlers||(wx.$onErrorHandlers=[]),wx.$onErrorHandlers.push(e))}},Rt={args(e){const t=getApp({allowDefault:!0})||{};if(t.$vm){if(e.__weh){const n=t.$vm.$.onError;if(n){const t=n.indexOf(e.__weh);t>-1&&n.splice(t,1)}}}else{if(!wx.$onErrorHandlers)return;const t=wx.$onErrorHandlers.findIndex((t=>t===e));-1!==t&&wx.$onErrorHandlers.splice(t,1)}}},Mt={args(){if(wx.__uni_console__){if(wx.__uni_console_warned__)return;wx.__uni_console_warned__=!0,console.warn("开发模式下小程序日志回显会使用 socket 连接，为了避免冲突，建议使用 SocketTask 的方式去管理 WebSocket 或手动关闭日志回显功能。[详情](https://uniapp.dcloud.net.cn/tutorial/run/mp-log.html)")}}},Tt=Mt,Vt={$on:ze,$off:Ke,$once:Fe,$emit:qe,upx2px:De,rpx2px:De,interceptors:{},addInterceptor:Be,removeInterceptor:Ue,onCreateVueApp:function(e){if(X)return e(X);Y.push(e)},invokeCreateVueAppHook:function(e){X=e,Y.forEach((t=>t(e)))},getLocale:mt,setLocale:e=>{const t=h(getApp)&&getApp();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,vt.forEach((t=>t({locale:e}))),!0)},onLocaleChange:e=>{-1===vt.indexOf(e)&&vt.push(e)},getPushClientId:et,onPushMessage:e=>{-1===ot.indexOf(e)&&ot.push(e)},offPushMessage:e=>{if(e){const t=ot.indexOf(e);t>-1&&ot.splice(t,1)}else ot.length=0},invokePushCallback:function(e){if("enabled"===e.type)Ze=!0;else if("clientId"===e.type)Ge=e.cid,Je=e.errMsg,Ye(Ge,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:Qe(e.message)};for(let e=0;e<ot.length;e++){if((0,ot[e])(t),t.stopped)break}}else"click"===e.type&&ot.forEach((t=>{t({type:"click",data:Qe(e.message)})}))},__f__:function(e,t,...n){t&&n.push(t),console[e].apply(console,n)}};const Dt=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],Ht=["lanDebug","router","worklet"],Nt=wx.getLaunchOptionsSync?wx.getLaunchOptionsSync():null;function Bt(e){return(!Nt||1154!==Nt.scene||!Ht.includes(e))&&(Dt.indexOf(e)>-1||"function"==typeof wx[e])}function Ut(){const e={};for(const t in wx)Bt(t)&&(e[t]=wx[t]);return"undefined"!=typeof globalThis&&"undefined"==typeof requireMiniProgram&&(globalThis.wx=e),e}const Wt=["__route__","__wxExparserNodeId__","__wxWebviewId__"],zt=(Ft={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]},function({service:e,success:t,fail:n,complete:o}){let r;Ft[e]?(r={errMsg:"getProvider:ok",service:e,provider:Ft[e]},h(t)&&t(r)):(r={errMsg:"getProvider:fail:服务["+e+"]不存在"},h(n)&&n(r)),h(o)&&o(r)});var Ft;const Kt=Ut();Kt.canIUse("getAppBaseInfo")||(Kt.getAppBaseInfo=Kt.getSystemInfoSync),Kt.canIUse("getWindowInfo")||(Kt.getWindowInfo=Kt.getSystemInfoSync),Kt.canIUse("getDeviceInfo")||(Kt.getDeviceInfo=Kt.getSystemInfoSync);let qt=Kt.getAppBaseInfo&&Kt.getAppBaseInfo();qt||(qt=Kt.getSystemInfoSync());const Gt=qt?qt.host:null,Jt=Gt&&"SAAASDK"===Gt.env?Kt.miniapp.shareVideoMessage:Kt.shareVideoMessage;var Zt=Object.freeze({__proto__:null,createSelectorQuery:function(){const e=Kt.createSelectorQuery(),t=e.in;return e.in=function(e){return e.$scope?t.call(this,e.$scope):t.call(this,function(e){const t=Object.create(null);return Wt.forEach((n=>{t[n]=e[n]})),t}(e))},e},getProvider:zt,shareVideoMessage:Jt});const Qt={args(e,t){e.compressedHeight&&!t.compressHeight&&(t.compressHeight=e.compressedHeight),e.compressedWidth&&!t.compressWidth&&(t.compressWidth=e.compressedWidth)}};var Xt=Object.freeze({__proto__:null,compressImage:Qt,getAppAuthorizeSetting:{returnValue:function(e,t){const{locationReducedAccuracy:n}=e;t.locationAccuracy="unsupported",!0===n?t.locationAccuracy="reduced":!1===n&&(t.locationAccuracy="full")}},getAppBaseInfo:At,getDeviceInfo:It,getSystemInfo:Ot,getSystemInfoSync:Pt,getWindowInfo:jt,offError:Rt,onError:Lt,onSocketMessage:Tt,onSocketOpen:Mt,previewImage:Ct,redirectTo:{},showActionSheet:Et});const Yt=Ut();var en=function(e,t,n=wx){const o=function(e){function t(e,t,n){return function(r){return t(o(e,r,n))}}function n(e,n,o={},r={},s=!1){if(w(n)){const i=!0===s?n:{};h(o)&&(o=o(n,i)||{});for(const c in n)if(u(o,c)){let t=o[c];h(t)&&(t=t(n[c],n,i)),t?g(t)?i[t]=n[c]:w(t)&&(i[t.name?t.name:c]=t.value):console.warn(`微信小程序 ${e} 暂不支持 ${c}`)}else if(-1!==gt.indexOf(c)){const o=n[c];h(o)&&(i[c]=t(e,o,r))}else s||u(i,c)||(i[c]=n[c]);return i}return h(n)&&(h(o)&&o(n,{}),n=t(e,n,r)),n}function o(t,o,r,s=!1){return h(e.returnValue)&&(o=e.returnValue(t,o)),n(t,o,r,{},s||!1)}return function(t,r){const s=u(e,t);if(!s&&"function"!=typeof wx[t])return r;const i=s||h(e.returnValue)||ut(t)||pt(t),c=s||h(r);if(!s&&!r)return function(){console.error(`微信小程序 暂不支持${t}`)};if(!i||!c)return r;const a=e[t];return function(e,r){let s=a||{};h(a)&&(s=a(e));const i=[e=n(t,e,s.args,s.returnValue)];void 0!==r&&i.push(r);const c=wx[s.name||t].apply(wx,i);return(ut(t)||pt(t))&&c&&!c.__v_skip&&(c.__v_skip=!0),ft(t)?o(t,c,s.returnValue,ut(t)):c}}}(t);return new Proxy({},{get:(t,r)=>u(t,r)?t[r]:u(e,r)?ht(r,e[r]):u(Vt,r)?ht(r,Vt[r]):ht(r,o(r,n[r]))})}(Zt,Xt,Yt);let tn,nn;class on{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=tn,!e&&tn&&(this.index=(tn.scopes||(tn.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=tn;try{return tn=this,e()}finally{tn=t}}}on(){tn=this}off(){tn=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}class rn{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=tn){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,pn();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),dn()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=ln,t=nn;try{return ln=!0,nn=this,this._runnings++,sn(this),this.fn()}finally{cn(this),this._runnings--,nn=t,ln=e}}stop(){var e;this.active&&(sn(this),cn(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function sn(e){e._trackId++,e._depsLength=0}function cn(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)an(e.deps[t],e);e.deps.length=e._depsLength}}function an(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let ln=!0,un=0;const fn=[];function pn(){fn.push(ln),ln=!1}function dn(){const e=fn.pop();ln=void 0===e||e}function hn(){un++}function gn(){for(un--;!un&&vn.length;)vn.shift()()}function mn(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&an(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const vn=[];function _n(e,t,n){hn();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&vn.push(o.scheduler)))}gn()}const yn=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},xn=new WeakMap,wn=Symbol(""),bn=Symbol("");function $n(e,t,n){if(ln&&nn){let t=xn.get(e);t||xn.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=yn((()=>t.delete(n)))),mn(nn,o)}}function kn(e,t,n,o,r,s){const i=xn.get(e);if(!i)return;let c=[];if("clear"===t)c=[...i.values()];else if("length"===n&&f(e)){const e=Number(o);i.forEach(((t,n)=>{("length"===n||!m(n)&&n>=e)&&c.push(t)}))}else switch(void 0!==n&&c.push(i.get(n)),t){case"add":f(e)?b(n)&&c.push(i.get("length")):(c.push(i.get(wn)),p(e)&&c.push(i.get(bn)));break;case"delete":f(e)||(c.push(i.get(wn)),p(e)&&c.push(i.get(bn)));break;case"set":p(e)&&c.push(i.get(wn))}hn();for(const a of c)a&&_n(a,4);gn()}const Sn=e("__proto__,__v_isRef,__isVue"),On=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(m)),Pn=Cn();function Cn(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=go(this);for(let t=0,r=this.length;t<r;t++)$n(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(go)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){pn(),hn();const n=go(this)[t].apply(this,e);return gn(),dn(),n}})),e}function En(e){const t=go(this);return $n(t,0,e),t.hasOwnProperty(e)}class In{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?io:so:r?ro:oo).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const s=f(e);if(!o){if(s&&u(Pn,t))return Reflect.get(Pn,t,n);if("hasOwnProperty"===t)return En}const i=Reflect.get(e,t,n);return(m(t)?On.has(t):Sn(t))?i:(o||$n(e,0,t),r?i:bo(i)?s&&b(t)?i:i.value:v(i)?o?lo(i):ao(i):i)}}class An extends In{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=po(r);if(ho(n)||po(n)||(r=go(r),n=go(n)),!f(e)&&bo(r)&&!bo(n))return!t&&(r.value=n,!0)}const s=f(e)&&b(t)?Number(t)<e.length:u(e,t),i=Reflect.set(e,t,n,o);return e===go(o)&&(s?A(n,r)&&kn(e,"set",t,n):kn(e,"add",t,n)),i}deleteProperty(e,t){const n=u(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&kn(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return m(t)&&On.has(t)||$n(e,0,t),n}ownKeys(e){return $n(e,0,f(e)?"length":wn),Reflect.ownKeys(e)}}class jn extends In{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const Ln=new An,Rn=new jn,Mn=new An(!0),Tn=e=>e,Vn=e=>Reflect.getPrototypeOf(e);function Dn(e,t,n=!1,o=!1){const r=go(e=e.__v_raw),s=go(t);n||(A(t,s)&&$n(r,0,t),$n(r,0,s));const{has:i}=Vn(r),c=o?Tn:n?_o:vo;return i.call(r,t)?c(e.get(t)):i.call(r,s)?c(e.get(s)):void(e!==r&&e.get(t))}function Hn(e,t=!1){const n=this.__v_raw,o=go(n),r=go(e);return t||(A(e,r)&&$n(o,0,e),$n(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function Nn(e,t=!1){return e=e.__v_raw,!t&&$n(go(e),0,wn),Reflect.get(e,"size",e)}function Bn(e){e=go(e);const t=go(this);return Vn(t).has.call(t,e)||(t.add(e),kn(t,"add",e,e)),this}function Un(e,t){t=go(t);const n=go(this),{has:o,get:r}=Vn(n);let s=o.call(n,e);s||(e=go(e),s=o.call(n,e));const i=r.call(n,e);return n.set(e,t),s?A(t,i)&&kn(n,"set",e,t):kn(n,"add",e,t),this}function Wn(e){const t=go(this),{has:n,get:o}=Vn(t);let r=n.call(t,e);r||(e=go(e),r=n.call(t,e)),o&&o.call(t,e);const s=t.delete(e);return r&&kn(t,"delete",e,void 0),s}function zn(){const e=go(this),t=0!==e.size,n=e.clear();return t&&kn(e,"clear",void 0,void 0),n}function Fn(e,t){return function(n,o){const r=this,s=r.__v_raw,i=go(s),c=t?Tn:e?_o:vo;return!e&&$n(i,0,wn),s.forEach(((e,t)=>n.call(o,c(e),c(t),r)))}}function Kn(e,t,n){return function(...o){const r=this.__v_raw,s=go(r),i=p(s),c="entries"===e||e===Symbol.iterator&&i,a="keys"===e&&i,l=r[e](...o),u=n?Tn:t?_o:vo;return!t&&$n(s,0,a?bn:wn),{next(){const{value:e,done:t}=l.next();return t?{value:e,done:t}:{value:c?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function qn(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Gn(){const e={get(e){return Dn(this,e)},get size(){return Nn(this)},has:Hn,add:Bn,set:Un,delete:Wn,clear:zn,forEach:Fn(!1,!1)},t={get(e){return Dn(this,e,!1,!0)},get size(){return Nn(this)},has:Hn,add:Bn,set:Un,delete:Wn,clear:zn,forEach:Fn(!1,!0)},n={get(e){return Dn(this,e,!0)},get size(){return Nn(this,!0)},has(e){return Hn.call(this,e,!0)},add:qn("add"),set:qn("set"),delete:qn("delete"),clear:qn("clear"),forEach:Fn(!0,!1)},o={get(e){return Dn(this,e,!0,!0)},get size(){return Nn(this,!0)},has(e){return Hn.call(this,e,!0)},add:qn("add"),set:qn("set"),delete:qn("delete"),clear:qn("clear"),forEach:Fn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Kn(r,!1,!1),n[r]=Kn(r,!0,!1),t[r]=Kn(r,!1,!0),o[r]=Kn(r,!0,!0)})),[e,n,t,o]}const[Jn,Zn,Qn,Xn]=Gn();function Yn(e,t){const n=t?e?Xn:Qn:e?Zn:Jn;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(u(n,o)&&o in t?n:t,o,r)}const eo={get:Yn(!1,!1)},to={get:Yn(!1,!0)},no={get:Yn(!0,!1)},oo=new WeakMap,ro=new WeakMap,so=new WeakMap,io=new WeakMap;function co(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>x(e).slice(8,-1))(e))}function ao(e){return po(e)?e:uo(e,!1,Ln,eo,oo)}function lo(e){return uo(e,!0,Rn,no,so)}function uo(e,t,n,o,r){if(!v(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const s=r.get(e);if(s)return s;const i=co(e);if(0===i)return e;const c=new Proxy(e,2===i?o:n);return r.set(e,c),c}function fo(e){return po(e)?fo(e.__v_raw):!(!e||!e.__v_isReactive)}function po(e){return!(!e||!e.__v_isReadonly)}function ho(e){return!(!e||!e.__v_isShallow)}function go(e){const t=e&&e.__v_raw;return t?go(t):e}function mo(e){return Object.isExtensible(e)&&((e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})})(e,"__v_skip",!0),e}const vo=e=>v(e)?ao(e):e,_o=e=>v(e)?lo(e):e;class yo{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new rn((()=>e(this._value)),(()=>wo(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=go(this);return e._cacheable&&!e.effect.dirty||!A(e._value,e._value=e.effect.run())||wo(e,4),xo(e),e.effect._dirtyLevel>=2&&wo(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function xo(e){var t;ln&&nn&&(e=go(e),mn(nn,null!=(t=e.dep)?t:e.dep=yn((()=>e.dep=void 0),e instanceof yo?e:void 0)))}function wo(e,t=4,n){const o=(e=go(e)).dep;o&&_n(o,t)}function bo(e){return!(!e||!0!==e.__v_isRef)}function $o(e){return function(e,t){if(bo(e))return e;return new ko(e,t)}(e,!1)}class ko{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:go(e),this._value=t?e:vo(e)}get value(){return xo(this),this._value}set value(e){const t=this.__v_isShallow||ho(e)||po(e);e=t?e:go(e),A(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:vo(e),wo(this,4))}}function So(e){return bo(e)?e.value:e}const Oo={get:(e,t,n)=>So(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return bo(r)&&!bo(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Po(e){return fo(e)?e:new Proxy(e,Oo)}function Co(e,t,n,o){try{return o?e(...o):e()}catch(r){Io(r,t,n)}}function Eo(e,t,n,o){if(h(e)){const r=Co(e,t,n,o);return r&&_(r)&&r.catch((e=>{Io(e,t,n)})),r}const r=[];for(let s=0;s<e.length;s++)r.push(Eo(e[s],t,n,o));return r}function Io(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,s=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,s))return;o=o.parent}const i=t.appContext.config.errorHandler;if(i)return void Co(i,null,10,[e,r,s])}Ao(e,n,r,o)}function Ao(e,t,n,o=!0){console.error(e)}let jo=!1,Lo=!1;const Ro=[];let Mo=0;const To=[];let Vo=null,Do=0;const Ho=Promise.resolve();let No=null;function Bo(e){const t=No||Ho;return e?t.then(this?e.bind(this):e):t}function Uo(e){Ro.length&&Ro.includes(e,jo&&e.allowRecurse?Mo+1:Mo)||(null==e.id?Ro.push(e):Ro.splice(function(e){let t=Mo+1,n=Ro.length;for(;t<n;){const o=t+n>>>1,r=Ro[o],s=Ko(r);s<e||s===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),Wo())}function Wo(){jo||Lo||(Lo=!0,No=Ho.then(Go))}function zo(e){f(e)?To.push(...e):Vo&&Vo.includes(e,e.allowRecurse?Do+1:Do)||To.push(e),Wo()}function Fo(e,t,n=(jo?Mo+1:0)){for(;n<Ro.length;n++){const e=Ro[n];e&&e.pre&&(Ro.splice(n,1),n--,e())}}const Ko=e=>null==e.id?1/0:e.id,qo=(e,t)=>{const n=Ko(e)-Ko(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Go(e){Lo=!1,jo=!0,Ro.sort(qo);try{for(Mo=0;Mo<Ro.length;Mo++){const e=Ro[Mo];e&&!1!==e.active&&Co(e,null,14)}}finally{Mo=0,Ro.length=0,function(e){if(To.length){const e=[...new Set(To)].sort(((e,t)=>Ko(e)-Ko(t)));if(To.length=0,Vo)return void Vo.push(...e);for(Vo=e,Do=0;Do<Vo.length;Do++)Vo[Do]();Vo=null,Do=0}}(),jo=!1,No=null,(Ro.length||To.length)&&Go()}}function Jo(e,n,...o){if(e.isUnmounted)return;const r=e.vnode.props||t;let s=o;const i=n.startsWith("update:"),c=i&&n.slice(7);if(c&&c in r){const e=`${"modelValue"===c?"model":c}Modifiers`,{number:n,trim:i}=r[e]||t;i&&(s=o.map((e=>g(e)?e.trim():e))),n&&(s=o.map(L))}let a,l=r[a=I(n)]||r[a=I(O(n))];!l&&i&&(l=r[a=I(C(n))]),l&&Eo(l,e,6,s);const u=r[a+"Once"];if(u){if(e.emitted){if(e.emitted[a])return}else e.emitted={};e.emitted[a]=!0,Eo(u,e,6,s)}}function Zo(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const s=e.emits;let i={},a=!1;if(!h(e)){const o=e=>{const n=Zo(e,t,!0);n&&(a=!0,c(i,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return s||a?(f(s)?s.forEach((e=>i[e]=null)):c(i,s),v(e)&&o.set(e,i),i):(v(e)&&o.set(e,null),null)}function Qo(e,t){return!(!e||!s(t))&&(t=t.slice(2).replace(/Once$/,""),u(e,t[0].toLowerCase()+t.slice(1))||u(e,C(t))||u(e,t))}let Xo=null;function Yo(e){const t=Xo;return Xo=e,e&&e.type.__scopeId,t}function er(e,t){return e&&(e[t]||e[O(t)]||e[E(O(t))])}const tr={};function nr(e,t,n){return or(e,t,n)}function or(e,n,{immediate:r,deep:s,flush:i,once:c,onTrack:l,onTrigger:u}=t){if(n&&c){const e=n;n=(...t)=>{e(...t),S()}}const p=os,d=e=>!0===s?e:ir(e,!1===s?1:void 0);let g,m,v=!1,_=!1;if(bo(e)?(g=()=>e.value,v=ho(e)):fo(e)?(g=()=>d(e),v=!0):f(e)?(_=!0,v=e.some((e=>fo(e)||ho(e))),g=()=>e.map((e=>bo(e)?e.value:fo(e)?d(e):h(e)?Co(e,p,2):void 0))):g=h(e)?n?()=>Co(e,p,2):()=>(m&&m(),Eo(e,p,3,[y])):o,n&&s){const e=g;g=()=>ir(e())}let y=e=>{m=$.onStop=()=>{Co(e,p,4),m=$.onStop=void 0}},x=_?new Array(e.length).fill(tr):tr;const w=()=>{if($.active&&$.dirty)if(n){const e=$.run();(s||v||(_?e.some(((e,t)=>A(e,x[t]))):A(e,x)))&&(m&&m(),Eo(n,p,3,[e,x===tr?void 0:_&&x[0]===tr?[]:x,y]),x=e)}else $.run()};let b;w.allowRecurse=!!n,"sync"===i?b=w:"post"===i?b=()=>Xr(w,p&&p.suspense):(w.pre=!0,p&&(w.id=p.uid),b=()=>Uo(w));const $=new rn(g,o,b),k=tn,S=()=>{$.stop(),k&&a(k.effects,$)};return n?r?w():x=$.run():"post"===i?Xr($.run.bind($),p&&p.suspense):$.run(),S}function rr(e,t,n){const o=this.proxy,r=g(e)?e.includes(".")?sr(o,e):()=>o[e]:e.bind(o,o);let s;h(t)?s=t:(s=t.handler,n=t);const i=cs(this),c=or(r,s.bind(o),n);return i(),c}function sr(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function ir(e,t,n=0,o){if(!v(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),bo(e))ir(e.value,t,n,o);else if(f(e))for(let r=0;r<e.length;r++)ir(e[r],t,n,o);else if(d(e)||p(e))e.forEach((e=>{ir(e,t,n,o)}));else if(w(e))for(const r in e)ir(e[r],t,n,o);return e}function cr(){return{app:null,config:{isNativeTag:r,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ar=0;let lr=null;function ur(e,t,n=!1){const o=os||Xo;if(o||lr){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:lr._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&h(t)?t.call(o&&o.proxy):t}}function fr(e,t){dr(e,"a",t)}function pr(e,t){dr(e,"da",t)}function dr(e,t,n=os){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(gr(t,o,n),n){let e=n.parent;for(;e&&e.parent;)e.parent.vnode.type.__isKeepAlive&&hr(o,t,n,e),e=e.parent}}function hr(e,t,n,o){const r=gr(t,e,o,!0);br((()=>{a(o[t],r)}),n)}function gr(e,t,n=os,o=!1){if(n){(function(e){return G.indexOf(e)>-1})(e)&&(n=n.root);const r=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;pn();const r=cs(n),s=Eo(t,n,e,o);return r(),dn(),s});return o?r.unshift(s):r.push(s),s}}const mr=e=>(t,n=os)=>(!us||"sp"===e)&&gr(e,((...e)=>t(...e)),n),vr=mr("bm"),_r=mr("m"),yr=mr("bu"),xr=mr("u"),wr=mr("bum"),br=mr("um"),$r=mr("sp"),kr=mr("rtg"),Sr=mr("rtc");function Or(e,t=os){gr("ec",e,t)}const Pr=e=>e?ls(e)?ds(e)||e.proxy:Pr(e.parent):null;const Cr=c(Object.create(null),{$:function(e){return e},$el:e=>e.__$el||(e.__$el={}),$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Pr(e.parent),$root:e=>Pr(e.root),$emit:e=>e.emit,$options:e=>Tr(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,Uo(e.update)}),$watch:e=>rr.bind(e)}),Er=(e,n)=>e!==t&&!e.__isScriptSetup&&u(e,n),Ir={get({_:e},n){const{ctx:o,setupState:r,data:s,props:i,accessCache:c,type:a,appContext:l}=e;let f;if("$"!==n[0]){const a=c[n];if(void 0!==a)switch(a){case 1:return r[n];case 2:return s[n];case 4:return o[n];case 3:return i[n]}else{if(Er(r,n))return c[n]=1,r[n];if(s!==t&&u(s,n))return c[n]=2,s[n];if((f=e.propsOptions[0])&&u(f,n))return c[n]=3,i[n];if(o!==t&&u(o,n))return c[n]=4,o[n];jr&&(c[n]=0)}}const p=Cr[n];let d,h;return p?("$attrs"===n&&$n(e,0,n),p(e)):(d=a.__cssModules)&&(d=d[n])?d:o!==t&&u(o,n)?(c[n]=4,o[n]):(h=l.config.globalProperties,u(h,n)?h[n]:void 0)},set({_:e},n,o){const{data:r,setupState:s,ctx:i}=e;return Er(s,n)?(s[n]=o,!0):r!==t&&u(r,n)?(r[n]=o,!0):!u(e.props,n)&&(("$"!==n[0]||!(n.slice(1)in e))&&(i[n]=o,!0))},has({_:{data:e,setupState:n,accessCache:o,ctx:r,appContext:s,propsOptions:i}},c){let a;return!!o[c]||e!==t&&u(e,c)||Er(n,c)||(a=i[0])&&u(a,c)||u(r,c)||u(Cr,c)||u(s.config.globalProperties,c)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:u(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ar(e){return f(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let jr=!0;function Lr(e){const t=Tr(e),n=e.proxy,r=e.ctx;jr=!1,t.beforeCreate&&Rr(t.beforeCreate,e,"bc");const{data:s,computed:i,methods:c,watch:a,provide:l,inject:u,created:p,beforeMount:d,mounted:g,beforeUpdate:m,updated:_,activated:y,deactivated:x,beforeDestroy:w,beforeUnmount:b,destroyed:$,unmounted:k,render:S,renderTracked:O,renderTriggered:P,errorCaptured:C,serverPrefetch:E,expose:I,inheritAttrs:A,components:j,directives:L,filters:R}=t;if(u&&function(e,t,n=o){f(e)&&(e=Nr(e));for(const o in e){const n=e[o];let r;r=v(n)?"default"in n?ur(n.from||o,n.default,!0):ur(n.from||o):ur(n),bo(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,r,null),c)for(const o in c){const e=c[o];h(e)&&(r[o]=e.bind(n))}if(s){const t=s.call(n,n);v(t)&&(e.data=ao(t))}if(jr=!0,i)for(const f in i){const e=i[f],t=h(e)?e.bind(n,n):h(e.get)?e.get.bind(n,n):o,s=!h(e)&&h(e.set)?e.set.bind(n):o,c=hs({get:t,set:s});Object.defineProperty(r,f,{enumerable:!0,configurable:!0,get:()=>c.value,set:e=>c.value=e})}if(a)for(const o in a)Mr(a[o],r,n,o);function M(e,t){f(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(function(){if(l){const e=h(l)?l.call(n):l;Reflect.ownKeys(e).forEach((t=>{!function(e,t){if(os){let n=os.provides;const o=os.parent&&os.parent.provides;o===n&&(n=os.provides=Object.create(o)),n[e]=t,"app"===os.type.mpType&&os.appContext.app.provide(e,t)}}(t,e[t])}))}}(),p&&Rr(p,e,"c"),M(vr,d),M(_r,g),M(yr,m),M(xr,_),M(fr,y),M(pr,x),M(Or,C),M(Sr,O),M(kr,P),M(wr,b),M(br,k),M($r,E),f(I))if(I.length){const t=e.exposed||(e.exposed={});I.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});S&&e.render===o&&(e.render=S),null!=A&&(e.inheritAttrs=A),j&&(e.components=j),L&&(e.directives=L),e.ctx.$onApplyOptions&&e.ctx.$onApplyOptions(t,e,n)}function Rr(e,t,n){Eo(f(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function Mr(e,t,n,o){const r=o.includes(".")?sr(n,o):()=>n[o];if(g(e)){const n=t[e];h(n)&&nr(r,n)}else if(h(e))nr(r,e.bind(n));else if(v(e))if(f(e))e.forEach((e=>Mr(e,t,n,o)));else{const o=h(e.handler)?e.handler.bind(n):t[e.handler];h(o)&&nr(r,o,e)}}function Tr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,c=s.get(t);let a;return c?a=c:r.length||n||o?(a={},r.length&&r.forEach((e=>Vr(a,e,i,!0))),Vr(a,t,i)):a=t,v(t)&&s.set(t,a),a}function Vr(e,t,n,o=!1){const{mixins:r,extends:s}=t;s&&Vr(e,s,n,!0),r&&r.forEach((t=>Vr(e,t,n,!0)));for(const i in t)if(o&&"expose"===i);else{const o=Dr[i]||n&&n[i];e[i]=o?o(e[i],t[i]):t[i]}return e}const Dr={data:Hr,props:Wr,emits:Wr,methods:Ur,computed:Ur,beforeCreate:Br,created:Br,beforeMount:Br,mounted:Br,beforeUpdate:Br,updated:Br,beforeDestroy:Br,beforeUnmount:Br,destroyed:Br,unmounted:Br,activated:Br,deactivated:Br,errorCaptured:Br,serverPrefetch:Br,components:Ur,directives:Ur,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=Br(e[o],t[o]);return n},provide:Hr,inject:function(e,t){return Ur(Nr(e),Nr(t))}};function Hr(e,t){return t?e?function(){return c(h(e)?e.call(this,this):e,h(t)?t.call(this,this):t)}:t:e}function Nr(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Br(e,t){return e?[...new Set([].concat(e,t))]:t}function Ur(e,t){return e?c(Object.create(null),e,t):t}function Wr(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:c(Object.create(null),Ar(e),Ar(null!=t?t:{})):t}function zr(e,t,n,o=!1){const r={},s={};e.propsDefaults=Object.create(null),Fr(e,t,r,s);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=o?r:uo(r,!1,Mn,to,ro):e.type.props?e.props=r:e.props=s,e.attrs=s}function Fr(e,n,o,r){const[s,i]=e.propsOptions;let c,a=!1;if(n)for(let t in n){if($(t))continue;const l=n[t];let f;s&&u(s,f=O(t))?i&&i.includes(f)?(c||(c={}))[f]=l:o[f]=l:Qo(e.emitsOptions,t)||t in r&&l===r[t]||(r[t]=l,a=!0)}if(i){const n=go(o),r=c||t;for(let t=0;t<i.length;t++){const c=i[t];o[c]=Kr(s,n,c,r[c],e,!u(r,c))}}return a}function Kr(e,t,n,o,r,s){const i=e[n];if(null!=i){const e=u(i,"default");if(e&&void 0===o){const e=i.default;if(i.type!==Function&&!i.skipFactory&&h(e)){const{propsDefaults:s}=r;if(n in s)o=s[n];else{const i=cs(r);o=s[n]=e.call(null,t),i()}}else o=e}i[0]&&(s&&!e?o=!1:!i[1]||""!==o&&o!==C(n)||(o=!0))}return o}function qr(e,o,r=!1){const s=o.propsCache,i=s.get(e);if(i)return i;const a=e.props,l={},p=[];let d=!1;if(!h(e)){const t=e=>{d=!0;const[t,n]=qr(e,o,!0);c(l,t),n&&p.push(...n)};!r&&o.mixins.length&&o.mixins.forEach(t),e.extends&&t(e.extends),e.mixins&&e.mixins.forEach(t)}if(!a&&!d)return v(e)&&s.set(e,n),n;if(f(a))for(let n=0;n<a.length;n++){const e=O(a[n]);Gr(e)&&(l[e]=t)}else if(a)for(const t in a){const e=O(t);if(Gr(e)){const n=a[t],o=l[e]=f(n)||h(n)?{type:n}:c({},n);if(o){const t=Qr(Boolean,o.type),n=Qr(String,o.type);o[0]=t>-1,o[1]=n<0||t<n,(t>-1||u(o,"default"))&&p.push(e)}}}const g=[l,p];return v(e)&&s.set(e,g),g}function Gr(e){return"$"!==e[0]&&!$(e)}function Jr(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function Zr(e,t){return Jr(e)===Jr(t)}function Qr(e,t){return f(t)?t.findIndex((t=>Zr(t,e))):h(t)&&Zr(t,e)?0:-1}const Xr=zo;function Yr(e){return e?fo(t=e)||po(t)||"__vInternal"in e?c({},e):e:null;var t}const es=cr();let ts=0;function ns(e,n,o){const r=e.type,s=(n?n.appContext:e.appContext)||es,i={uid:ts++,vnode:e,type:r,parent:n,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new on(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:qr(r,s),emitsOptions:Zo(r,s),emit:null,emitted:null,propsDefaults:t,inheritAttrs:r.inheritAttrs,ctx:t,data:t,props:t,attrs:t,slots:t,refs:t,setupState:t,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:o,suspenseId:o?o.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null,$uniElements:new Map,$templateUniElementRefs:[],$templateUniElementStyles:{},$eS:{},$eA:{}};return i.ctx={_:i},i.root=n?n.root:i,i.emit=Jo.bind(null,i),e.ce&&e.ce(i),i}let os=null;const rs=()=>os||Xo;let ss,is;ss=e=>{os=e},is=e=>{us=e};const cs=e=>{const t=os;return ss(e),e.scope.on(),()=>{e.scope.off(),ss(t)}},as=()=>{os&&os.scope.off(),ss(null)};function ls(e){return 4&e.vnode.shapeFlag}let us=!1;function fs(e,t=!1){t&&is(t);const{props:n}=e.vnode,o=ls(e);zr(e,n,o,t);const r=o?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=mo(new Proxy(e.ctx,Ir));const{setup:o}=n;if(o){const t=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>($n(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,n=cs(e);pn();const r=Co(o,e,0,[e.props,t]);dn(),n(),_(r)?r.then(as,as):function(e,t,n){h(t)?e.render=t:v(t)&&(e.setupState=Po(t));ps(e)}(e,r)}else ps(e)}(e):void 0;return t&&is(!1),r}function ps(e,t,n){const r=e.type;e.render||(e.render=r.render||o);{const t=cs(e);pn();try{Lr(e)}finally{dn(),t()}}}function ds(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Po(mo(e.exposed)),{get:(t,n)=>n in t?t[n]:e.proxy[n],has:(e,t)=>t in e||t in Cr}))}const hs=(e,t)=>{const n=function(e,t,n=!1){let r,s;const i=h(e);return i?(r=e,s=o):(r=e.get,s=e.set),new yo(r,s,i||!s,n)}(e,0,us);return n},gs="3.4.21";function ms(e){return So(e)}const vs="[object Array]",_s="[object Object]";function ys(e,t){const n={};return xs(e,t),ws(e,t,"",n),n}function xs(e,t){if((e=ms(e))===t)return;const n=x(e),o=x(t);if(n==_s&&o==_s)for(let r in t){const n=e[r];void 0===n?e[r]=null:xs(n,t[r])}else n==vs&&o==vs&&e.length>=t.length&&t.forEach(((t,n)=>{xs(e[n],t)}))}function ws(e,t,n,o){if((e=ms(e))===t)return;const r=x(e),s=x(t);if(r==_s)if(s!=_s||Object.keys(e).length<Object.keys(t).length)bs(o,n,e);else for(let i in e){const r=ms(e[i]),s=t[i],c=x(r),a=x(s);if(c!=vs&&c!=_s)r!=s&&bs(o,(""==n?"":n+".")+i,r);else if(c==vs)a!=vs||r.length<s.length?bs(o,(""==n?"":n+".")+i,r):r.forEach(((e,t)=>{ws(e,s[t],(""==n?"":n+".")+i+"["+t+"]",o)}));else if(c==_s)if(a!=_s||Object.keys(r).length<Object.keys(s).length)bs(o,(""==n?"":n+".")+i,r);else for(let e in r)ws(r[e],s[e],(""==n?"":n+".")+i+"."+e,o)}else r==vs?s!=vs||e.length<t.length?bs(o,n,e):e.forEach(((e,r)=>{ws(e,t[r],n+"["+r+"]",o)})):bs(o,n,e)}function bs(e,t,n){e[t]=n}function $s(e){const t=e.ctx.__next_tick_callbacks;if(t&&t.length){const e=t.slice(0);t.length=0;for(let t=0;t<e.length;t++)e[t]()}}function ks(e,t){const n=e.ctx;if(!n.__next_tick_pending&&!function(e){return Ro.includes(e.update)}(e))return Bo(t&&t.bind(e.proxy));let o;return n.__next_tick_callbacks||(n.__next_tick_callbacks=[]),n.__next_tick_callbacks.push((()=>{t?Co(t.bind(e.proxy),e,14):o&&o(e.proxy)})),new Promise((e=>{o=e}))}function Ss(e,t){const n=typeof(e=ms(e));if("object"===n&&null!==e){let n=t.get(e);if(void 0!==n)return n;if(f(e)){const o=e.length;n=new Array(o),t.set(e,n);for(let r=0;r<o;r++)n[r]=Ss(e[r],t)}else{n={},t.set(e,n);for(const o in e)u(e,o)&&(n[o]=Ss(e[o],t))}return n}if("symbol"!==n)return e}function Os(e){return Ss(e,"undefined"!=typeof WeakMap?new WeakMap:new Map)}function Ps(e,t,n){if(!t)return;(t=Os(t)).$eS=e.$eS||{},t.$eA=e.$eA||{};const o=e.ctx,r=o.mpType;if("page"===r||"component"===r){t.r0=1;const r=o.$scope,s=Object.keys(t),i=ys(t,n||function(e,t){const n=e.data,o=Object.create(null);return t.forEach((e=>{o[e]=n[e]})),o}(r,s));Object.keys(i).length?(o.__next_tick_pending=!0,r.setData(i,(()=>{o.__next_tick_pending=!1,$s(e)})),Fo()):$s(e)}}function Cs(e,t,n){t.appContext.config.globalProperties.$applyOptions(e,t,n);const o=e.computed;if(o){const e=Object.keys(o);if(e.length){const n=t.ctx;n.$computedKeys||(n.$computedKeys=[]),n.$computedKeys.push(...e)}}delete t.ctx.$onApplyOptions}function Es(e,t=!1){const{setupState:n,$templateRefs:o,$templateUniElementRefs:r,ctx:{$scope:s,$mpPlatform:i}}=e;if("mp-alipay"===i)return;if(!s||!o&&!r)return;if(t)return o&&o.forEach((e=>Is(e,null,n))),void(r&&r.forEach((e=>Is(e,null,n))));const c="mp-baidu"===i||"mp-toutiao"===i,a=e=>{if(0===e.length)return[];const t=(s.selectAllComponents(".r")||[]).concat(s.selectAllComponents(".r-i-f")||[]);return e.filter((e=>{const o=function(e,t){const n=e.find((e=>e&&(e.properties||e.props).uI===t));if(n){const e=n.$vm;return e?ds(e.$)||e:function(e){v(e)&&mo(e);return e}(n)}return null}(t,e.i);return!(!c||null!==o)||(Is(e,o,n),!1)}))},l=()=>{if(o){const t=a(o);t.length&&e.proxy&&e.proxy.$scope&&e.proxy.$scope.setData({r1:1},(()=>{a(t)}))}};r&&r.length&&ks(e,(()=>{r.forEach((e=>{f(e.v)?e.v.forEach((t=>{Is(e,t,n)})):Is(e,e.v,n)}))})),s._$setRef?s._$setRef(l):ks(e,l)}function Is({r:e,f:t},n,o){if(h(e))e(n,{});else{const r=g(e),s=bo(e);if(r||s)if(t){if(!s)return;f(e.value)||(e.value=[]);const t=e.value;if(-1===t.indexOf(n)){if(t.push(n),!n)return;n.$&&wr((()=>a(t,n)),n.$)}}else r?u(o,e)&&(o[e]=n):bo(e)&&(e.value=n)}}const As=zo;function js(e,t){const n=e.component=ns(e,t.parentComponent,null);return n.renderer=t.mpType?t.mpType:"component",n.ctx.$onApplyOptions=Cs,n.ctx.$children=[],"app"===t.mpType&&(n.render=o),t.onBeforeSetup&&t.onBeforeSetup(n,t),fs(n),t.parentComponent&&n.proxy&&t.parentComponent.ctx.$children.push(ds(n)||n.proxy),function(e){const t=Ms.bind(e);e.$updateScopedSlots=()=>Bo((()=>Uo(t)));const n=()=>{if(e.isMounted){const{next:t,bu:n,u:o}=e;Ts(e,!1),pn(),Fo(),dn(),n&&j(n),Ts(e,!0),Ps(e,Ls(e)),o&&As(o)}else wr((()=>{Es(e,!0)}),e),Ps(e,Ls(e))},r=e.effect=new rn(n,o,(()=>Uo(s)),e.scope),s=e.update=()=>{r.dirty&&r.run()};s.id=e.uid,Ts(e,!0),s()}(n),n.proxy}function Ls(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[c],slots:a,attrs:l,emit:u,render:f,renderCache:p,data:d,setupState:h,ctx:g,uid:m,appContext:{app:{config:{globalProperties:{pruneComponentPropsCache:v}}}},inheritAttrs:_}=e;let y;e.$uniElementIds=new Map,e.$templateRefs=[],e.$templateUniElementRefs=[],e.$templateUniElementStyles={},e.$ei=0,v(m),e.__counter=0===e.__counter?1:0;const x=Yo(e);try{if(4&n.shapeFlag){Rs(_,i,c,l);const e=r||o;y=f.call(e,e,p,i,h,d,g)}else{Rs(_,i,c,t.props?l:(e=>{let t;for(const n in e)("class"===n||"style"===n||s(n))&&((t||(t={}))[n]=e[n]);return t})(l));const e=t;y=e.length>1?e(i,{attrs:l,slots:a,emit:u}):e(i,null)}}catch(w){Io(w,e,1),y=!1}return Es(e),Yo(x),y}function Rs(e,t,n,o){if(t&&o&&!1!==e){const e=Object.keys(o).filter((e=>"class"!==e&&"style"!==e));if(!e.length)return;n&&e.some(i)?e.forEach((e=>{i(e)&&e.slice(9)in n||(t[e]=o[e])})):e.forEach((e=>t[e]=o[e]))}}function Ms(){const e=this.$scopedSlotsData;if(!e||0===e.length)return;const t=this.ctx.$scope,n=t.data,o=Object.create(null);e.forEach((({path:e,index:t,data:r})=>{const s=W(n,e),i=g(t)?`${e}.${t}`:`${e}[${t}]`;if(void 0===s||void 0===s[t])o[i]=r;else{const e=ys(r,s[t]);Object.keys(e).forEach((t=>{o[i+"."+t]=e[t]}))}})),e.length=0,Object.keys(o).length&&t.setData(o)}function Ts({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}const Vs=function(e,t=null){h(e)||(e=c({},e)),null==t||v(t)||(t=null);const n=cr(),o=new WeakSet,r=n.app={_uid:ar++,_component:e,_props:t,_container:null,_context:n,_instance:null,version:gs,get config(){return n.config},set config(e){},use:(e,...t)=>(o.has(e)||(e&&h(e.install)?(o.add(e),e.install(r,...t)):h(e)&&(o.add(e),e(r,...t))),r),mixin:e=>(n.mixins.includes(e)||n.mixins.push(e),r),component:(e,t)=>t?(n.components[e]=t,r):n.components[e],directive:(e,t)=>t?(n.directives[e]=t,r):n.directives[e],mount(){},unmount(){},provide:(e,t)=>(n.provides[e]=t,r),runWithContext(e){const t=lr;lr=r;try{return e()}finally{lr=t}}};return r};function Ds(e,t=null){("undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof my?my:void 0).__VUE__=!0;const n=Vs(e,t),r=n._context;r.config.globalProperties.$nextTick=function(e){return ks(this.$,e)};const s=e=>(e.appContext=r,e.shapeFlag=6,e),i=function(e,t){return js(s(e),t)},c=function(e){return e&&function(e){const{bum:t,scope:n,update:o,um:r}=e;t&&j(t);{const t=e.parent;if(t){const n=t.ctx.$children,o=ds(e)||e.proxy,r=n.indexOf(o);r>-1&&n.splice(r,1)}}n.stop(),o&&(o.active=!1),r&&As(r),As((()=>{e.isUnmounted=!0}))}(e.$)};return n.mount=function(){e.render=o;const t=js(s({type:e}),{mpType:"app",mpInstance:null,parentComponent:null,slots:[],props:null});return n._instance=t.$,t.$app=n,t.$createComponent=i,t.$destroyComponent=c,r.$appInstance=t,t},n.unmount=function(){},n}function Hs(e,t,n,o){h(t)&&gr(e,t.bind(n),o)}function Ns(e,t,n){!function(e,t,n){const o=e.mpType||n.$mpType;!o||"component"===o||"page"===o&&"component"===t.renderer||Object.keys(e).forEach((o=>{if(Q(o,e[o],!1)){const r=e[o];f(r)?r.forEach((e=>Hs(o,e,n,t))):Hs(o,r,n,t)}}))}(e,t,n)}function Bs(e,t,n){return e[t]=n}function Us(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Ws(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const s=e._instance;if(!s||!s.proxy)throw n;s.onError?s.proxy.$callHook("onError",n):Ao(n,0,o&&o.$.vnode,!1)}}function zs(e,t){return e?[...new Set([].concat(e,t))]:t}let Fs;const Ks="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",qs=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Gs(){const e=en.getStorageSync("uni_id_token")||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((o=t[1],decodeURIComponent(Fs(o).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(r){throw new Error("获取当前用户信息出错，详细错误信息为："+r.message)}var o;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}function Js(e){const t=e.config;var n;t.errorHandler=ee(e,Ws),n=t.optionMergeStrategies,J.forEach((e=>{n[e]=zs}));const o=t.globalProperties;!function(e){e.uniIDHasRole=function(e){const{role:t}=Gs();return t.indexOf(e)>-1},e.uniIDHasPermission=function(e){const{permission:t}=Gs();return this.uniIDHasRole("admin")||t.indexOf(e)>-1},e.uniIDTokenValid=function(){const{tokenExpired:e}=Gs();return e>Date.now()}}(o),o.$set=Bs,o.$applyOptions=Ns,o.$callMethod=Us,en.invokeCreateVueAppHook(e)}Fs="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!qs.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,o,r="",s=0;s<e.length;)t=Ks.indexOf(e.charAt(s++))<<18|Ks.indexOf(e.charAt(s++))<<12|(n=Ks.indexOf(e.charAt(s++)))<<6|(o=Ks.indexOf(e.charAt(s++))),r+=64===n?String.fromCharCode(t>>16&255):64===o?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;const Zs=Object.create(null);function Qs(e){delete Zs[e]}function Xs(e){if(!e)return;const[t,n]=e.split(",");return Zs[t]?Zs[t][parseInt(n)]:void 0}var Ys={install(e){Js(e),e.config.globalProperties.pruneComponentPropsCache=Qs;const t=e.mount;e.mount=function(n){const o=t.call(e,n),r=function(){const e="createApp";if("undefined"!=typeof global&&void 0!==global[e])return global[e];if("undefined"!=typeof my)return my[e]}();return r?r(o):"undefined"!=typeof createMiniProgramApp&&createMiniProgramApp(o),o}}};function ei(e){return g(e)?e:function(e){let t="";if(!e||g(e))return t;for(const n in e)t+=`${n.startsWith("--")?n:C(n)}:${e[n]};`;return t}(R(e))}function ti(e,t){const n=rs(),r=n.ctx,s=void 0===t||"mp-weixin"!==r.$mpPlatform&&"mp-qq"!==r.$mpPlatform&&"mp-xhs"!==r.$mpPlatform||!g(t)&&"number"!=typeof t?"":"_"+t,i="e"+n.$ei+++s,a=r.$scope;if(!e)return delete a[i],i;const l=a[i];return l?l.value=e:a[i]=function(e,t){const n=e=>{var r;(r=e).type&&r.target&&(r.preventDefault=o,r.stopPropagation=o,r.stopImmediatePropagation=o,u(r,"detail")||(r.detail={}),u(r,"markerId")&&(r.detail="object"==typeof r.detail?r.detail:{},r.detail.markerId=r.markerId),w(r.detail)&&u(r.detail,"checked")&&!u(r.detail,"value")&&(r.detail.value=r.detail.checked),w(r.detail)&&(r.target=c({},r.target,r.detail)));let s=[e];t&&t.ctx.$getTriggerEventDetail&&"number"==typeof e.detail&&(e.detail=t.ctx.$getTriggerEventDetail(e.detail)),e.detail&&e.detail.__args__&&(s=e.detail.__args__);const i=n.value,a=()=>Eo(function(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n&&n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e(t)))}return t}(e,i),t,5,s),l=e.target,p=!!l&&(!!l.dataset&&"true"===String(l.dataset.eventsync));if(!ni.includes(e.type)||p){const t=a();if("input"===e.type&&(f(t)||_(t)))return;return t}setTimeout(a)};return n.value=e,n}(e,n),i}const ni=["tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend","touchforcechange"];function oi(e,t={},n){const o=rs(),{parent:r,isMounted:s,ctx:{$scope:i}}=o,c=(i.properties||i.props).uI;if(!c)return;if(!r&&!s)return void _r((()=>{oi(e,t,n)}),o);const a=function(e,t){let n=t.parent;for(;n;){const t=n.$ssi;if(t&&t[e])return t[e];n=n.parent}}(c,o);a&&a(e,t,n)}const ri=function(e,t=null){return e&&(e.mpType="app"),Ds(e,t).use(Ys)};const si=["externalClasses"];const ii=/_(.*)_worklet_factory_/;function ci(e,t){const n=e.$children;for(let r=n.length-1;r>=0;r--){const e=n[r];if(e.$scope._$vueId===t)return e}let o;for(let r=n.length-1;r>=0;r--)if(o=ci(n[r],t),o)return o}const ai=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];function li(e,t){const n=e.ctx;n.mpType=t.mpType,n.$mpType=t.mpType,n.$mpPlatform="mp-weixin",n.$scope=t.mpInstance,Object.defineProperties(n,{virtualHostId:{get(){const e=this.$scope.data.virtualHostId;return void 0===e?"":e}}}),n.$mp={},n._self={},e.slots={},f(t.slots)&&t.slots.length&&(t.slots.forEach((t=>{e.slots[t]=!0})),e.slots.d&&(e.slots.default=!0)),n.getOpenerEventChannel=function(){return t.mpInstance.getOpenerEventChannel()},n.$hasHook=ui,n.$callHook=fi,e.emit=function(e,t){return function(n,...o){const r=t.$scope;if(r&&n){const e={__args__:o};r.triggerEvent(n,e)}return e.apply(this,[n,...o])}}(e.emit,n)}function ui(e){const t=this.$[e];return!(!t||!t.length)}function fi(e,t){"mounted"===e&&(fi.call(this,"bm"),this.$.isMounted=!0,e="m");const n=this.$[e];return n&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(n,t)}const pi=["onLoad","onShow","onHide","onUnload","onResize","onTabItemTap","onReachBottom","onPullDownRefresh","onAddToFavorites"];function di(e,t=new Set){if(e){Object.keys(e).forEach((n=>{Q(n,e[n])&&t.add(n)}));{const{extends:n,mixins:o}=e;o&&o.forEach((e=>di(e,t))),n&&di(n,t)}}return t}function hi(e,t,n){-1!==n.indexOf(t)||u(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.$callHook(t,e)})}const gi=["onReady"];function mi(e,t,n=gi){t.forEach((t=>hi(e,t,n)))}function vi(e,t,n=gi){di(t).forEach((t=>hi(e,t,n)))}const _i=U((()=>{const e=[],t=h(getApp)&&getApp({allowDefault:!0});if(t&&t.$vm&&t.$vm.$){const n=t.$vm.$.appContext.mixins;if(f(n)){const t=Object.keys(Z);n.forEach((n=>{t.forEach((t=>{u(n,t)&&!e.includes(t)&&e.push(t)}))}))}}return e}));const yi=["onShow","onHide","onError","onThemeChange","onPageNotFound","onUnhandledRejection"];function xi(e,t){const n=e.$,o={globalData:e.$options&&e.$options.globalData||{},$vm:e,onLaunch(t){this.$vm=e;const o=n.ctx;this.$vm&&o.$scope&&o.$callHook||(li(n,{mpType:"app",mpInstance:this,slots:[]}),o.globalData=this.globalData,e.$callHook("onLaunch",t))}},r=wx.$onErrorHandlers;r&&(r.forEach((e=>{gr("onError",e,n)})),r.length=0),function(e){const t=$o(function(){var e;let t="";{const n=(null===(e=wx.getAppBaseInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync();t=le(n&&n.language?n.language:"en")||"en"}return t}());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(e);const s=e.$.type;mi(o,yi),vi(o,s);{const e=s.methods;e&&c(o,e)}return o}function wi(e,t){if(h(e.onLaunch)){const t=wx.getLaunchOptionsSync&&wx.getLaunchOptionsSync();e.onLaunch(t)}h(e.onShow)&&wx.onAppShow&&wx.onAppShow((e=>{t.$callHook("onShow",e)})),h(e.onHide)&&wx.onAppHide&&wx.onAppHide((e=>{t.$callHook("onHide",e)}))}const bi=["eO","uR","uRIF","uI","uT","uP","uS"];function $i(e){e.properties||(e.properties={}),c(e.properties,function(e,t=!1){const n={};if(!t){let e=function(e){const t=Object.create(null);e&&e.forEach((e=>{t[e]=!0})),this.setData({$slots:t})};bi.forEach((e=>{n[e]={type:null,value:""}})),n.uS={type:null,value:[]},n.uS.observer=e}return e.behaviors&&e.behaviors.includes("wx://form-field")&&(e.properties&&e.properties.name||(n.name={type:null,value:""}),e.properties&&e.properties.value||(n.value={type:null,value:""})),n}(e),function(e){const t={};return e&&e.virtualHost&&(t.virtualHostStyle={type:null,value:""},t.virtualHostClass={type:null,value:""},t.virtualHostHidden={type:null,value:""},t.virtualHostId={type:null,value:""}),t}(e.options))}const ki=[String,Number,Boolean,Object,Array,null];function Si(e,t){const n=function(e,t){return f(e)&&1===e.length?e[0]:e}(e);return-1!==ki.indexOf(n)?n:null}function Oi(e,t){return(t?function(e){const t={};w(e)&&Object.keys(e).forEach((n=>{-1===bi.indexOf(n)&&(t[n]=e[n])}));return t}(e):Xs(e.uP))||{}}function Pi(e){const t=function(){const e=this.properties.uP;e&&(this.$vm?function(e,t){const n=go(t.props),o=Xs(e)||{};Ci(n,o)&&(!function(e,t,n,o){const{props:r,attrs:s,vnode:{patchFlag:i}}=e,c=go(r),[a]=e.propsOptions;let l=!1;if(!(o||i>0)||16&i){let o;Fr(e,t,r,s)&&(l=!0);for(const s in c)t&&(u(t,s)||(o=C(s))!==s&&u(t,o))||(a?!n||void 0===n[s]&&void 0===n[o]||(r[s]=Kr(a,c,s,void 0,e,!0)):delete r[s]);if(s!==c)for(const e in s)t&&u(t,e)||(delete s[e],l=!0)}else if(8&i){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let i=n[o];if(Qo(e.emitsOptions,i))continue;const f=t[i];if(a)if(u(s,i))f!==s[i]&&(s[i]=f,l=!0);else{const t=O(i);r[t]=Kr(a,c,t,f,e,!1)}else f!==s[i]&&(s[i]=f,l=!0)}}l&&kn(e,"set","$attrs")}(t,o,n,!1),r=t.update,Ro.indexOf(r)>-1&&function(e){const t=Ro.indexOf(e);t>Mo&&Ro.splice(t,1)}(t.update),t.update());var r}(e,this.$vm.$):"m"===this.properties.uT&&function(e,t){const n=t.properties,o=Xs(e)||{};Ci(n,o,!1)&&t.setData(o)}(e,this))};e.observers||(e.observers={}),e.observers.uP=t}function Ci(e,t,n=!0){const o=Object.keys(t);if(n&&o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const n=o[r];if(t[n]!==e[n])return!0}return!1}function Ei(e,t){e.data={},e.behaviors=function(e){const t=e.behaviors;let n=e.props;n||(e.props=n=[]);const o=[];return f(t)&&t.forEach((e=>{o.push(e.replace("uni://","wx://")),"uni://form-field"===e&&(f(n)?(n.push("name"),n.push("modelValue")):(n.name={type:String,default:""},n.modelValue={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),o}(t)}function Ii(e,{parse:t,mocks:n,isPage:o,isPageInProject:r,initRelation:s,handleLink:i,initLifetimes:a}){e=e.default||e;const l={multipleSlots:!0,addGlobalClass:!0,pureDataPattern:/^uP$/};f(e.mixins)&&e.mixins.forEach((e=>{v(e.options)&&c(l,e.options)})),e.options&&c(l,e.options);const p={options:l,lifetimes:a({mocks:n,isPage:o,initRelation:s,vueOptions:e}),pageLifetimes:{show(){this.$vm&&this.$vm.$callHook("onPageShow")},hide(){this.$vm&&this.$vm.$callHook("onPageHide")},resize(e){this.$vm&&this.$vm.$callHook("onPageResize",e)}},methods:{__l:i}};var d,h,g,m;return Ei(p,e),$i(p),Pi(p),function(e,t){si.forEach((n=>{u(t,n)&&(e[n]=t[n])}))}(p,e),d=p.methods,h=e.wxsCallMethods,f(h)&&h.forEach((e=>{d[e]=function(t){return this.$vm[e](t)}})),g=p.methods,(m=e.methods)&&Object.keys(m).forEach((e=>{const t=e.match(ii);if(t){const n=t[1];g[e]=m[e],g[n]=m[n]}})),t&&t(p,{handleLink:i}),p}let Ai,ji;function Li(){return getApp().$vm}function Ri(e,t){const{parse:n,mocks:o,isPage:r,initRelation:s,handleLink:i,initLifetimes:c}=t,a=Ii(e,{mocks:o,isPage:r,isPageInProject:!0,initRelation:s,handleLink:i,initLifetimes:c});!function({properties:e},t){f(t)?t.forEach((t=>{e[t]={type:String,value:""}})):w(t)&&Object.keys(t).forEach((n=>{const o=t[n];if(w(o)){let t=o.default;h(t)&&(t=t());const r=o.type;o.type=Si(r),e[n]={type:o.type,value:t}}else e[n]={type:Si(o)}}))}(a,(e.default||e).props);const l=a.methods;return l.onLoad=function(e){var t;return this.options=e,this.$page={fullPath:(t=this.route+q(e),function(e){return 0===e.indexOf("/")}(t)?t:"/"+t)},this.$vm&&this.$vm.$callHook("onLoad",e)},mi(l,pi),vi(l,e),function(e,t){if(!t)return;Object.keys(Z).forEach((n=>{t&Z[n]&&hi(e,n,[])}))}(l,e.__runtimeHooks),mi(l,_i()),n&&n(a,{handleLink:i}),a}const Mi=Page,Ti=Component;function Vi(e){const t=e.triggerEvent,n=function(n,...o){return t.apply(e,[(r=n,O(r.replace(F,"-"))),...o]);var r};try{e.triggerEvent=n}catch(o){e._triggerEvent=n}}function Di(e,t,n){const o=t[e];t[e]=o?function(...e){return Vi(this),o.apply(this,e)}:function(){Vi(this)}}Page=function(e){return Di("onLoad",e),Mi(e)},Component=function(e){Di("created",e);return e.properties&&e.properties.uP||($i(e),Pi(e)),Ti(e)};var Hi=Object.freeze({__proto__:null,handleLink:function(e){const t=e.detail||e.value,n=t.vuePid;let o;n&&(o=ci(this.$vm,n)),o||(o=this.$vm),t.parent=o},initLifetimes:function({mocks:e,isPage:t,initRelation:n,vueOptions:o}){return{attached(){let r=this.properties;!function(e,t){if(!e)return;const n=e.split(","),o=n.length;1===o?t._$vueId=n[0]:2===o&&(t._$vueId=n[0],t._$vuePid=n[1])}(r.uI,this);const s={vuePid:this._$vuePid};n(this,s);const i=this,c=t(i);let a=r;this.$vm=function(e,t){Ai||(Ai=Li().$createComponent);const n=Ai(e,t);return ds(n.$)||n}({type:o,props:Oi(a,c)},{mpType:c?"page":"component",mpInstance:i,slots:r.uS||{},parentComponent:s.parent&&s.parent.$,onBeforeSetup(t,n){!function(e,t){Object.defineProperty(e,"refs",{get(){const e={};return function(e,t,n){e.selectAllComponents(t).forEach((e=>{const t=e.properties.uR;n[t]=e.$vm||e}))}(t,".r",e),t.selectAllComponents(".r-i-f").forEach((t=>{const n=t.properties.uR;n&&(e[n]||(e[n]=[]),e[n].push(t.$vm||t))})),e}})}(t,i),function(e,t,n){const o=e.ctx;n.forEach((n=>{u(t,n)&&(e[n]=o[n]=t[n])}))}(t,i,e),function(e,t){li(e,t);const n=e.ctx;ai.forEach((e=>{n[e]=function(...t){const o=n.$scope;if(o&&o[e])return o[e].apply(o,t)}}))}(t,n)}}),c||function(e){const t=e.$options;f(t.behaviors)&&t.behaviors.includes("uni://form-field")&&e.$watch("modelValue",(()=>{e.$scope&&e.$scope.setData({name:e.name,value:e.modelValue})}),{immediate:!0})}(this.$vm)},ready(){this.$vm&&(this.$vm.$callHook("mounted"),this.$vm.$callHook("onReady"))},detached(){var e;this.$vm&&(Qs(this.$vm.$.uid),e=this.$vm,ji||(ji=Li().$destroyComponent),ji(e))}}},initRelation:function(e,t){e.triggerEvent("__l",t)},isPage:function(e){return!!e.route},mocks:["__route__","__wxExparserNodeId__","__wxWebviewId__"]});const Ni=function(e){return App(xi(e))},Bi=(Ui=Hi,function(e){return Component(Ri(e,Ui))});var Ui;const Wi=function(e){return function(t){return Component(Ii(t,e))}}(Hi),zi=function(e){wi(xi(e),e)},Fi=function(e){const t=xi(e),n=h(getApp)&&getApp({allowDefault:!0});if(!n)return;e.$.ctx.$scope=n;const o=n.globalData;o&&Object.keys(t.globalData).forEach((e=>{u(o,e)||(o[e]=t.globalData[e])})),Object.keys(t).forEach((e=>{u(n,e)||(n[e]=t[e])})),wi(t,e)};wx.createApp=global.createApp=Ni,wx.createPage=Bi,wx.createComponent=Wi,wx.createPluginApp=global.createPluginApp=zi,wx.createSubpackageApp=global.createSubpackageApp=Fi;const Ki=(e,t=0)=>(t,n=rs())=>{!us&&gr(e,t,n)},qi=Ki("onShow",3),Gi=Ki("onLoad",2),Ji=Ki("onUnload",2),Zi=Ki("onBackPress",2),Qi=Ki("onReachBottom",2),Xi=Ki("onPullDownRefresh",2),Yi=Ki("onShareAppMessage",2);exports._export_sfc=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},exports.computed=hs,exports.createSSRApp=ri,exports.e=(e,...t)=>c(e,...t),exports.f=(e,t)=>function(e,t){let n;if(f(e)||g(e)){n=new Array(e.length);for(let o=0,r=e.length;o<r;o++)n[o]=t(e[o],o,o)}else if("number"==typeof e){n=new Array(e);for(let o=0;o<e;o++)n[o]=t(o+1,o,o)}else if(v(e))if(e[Symbol.iterator])n=Array.from(e,((e,n)=>t(e,n,n)));else{const o=Object.keys(e);n=new Array(o.length);for(let r=0,s=o.length;r<s;r++){const s=o[r];n[r]=t(e[s],s,r)}}else n=[];return n}(e,t),exports.getCurrentInstance=rs,exports.index=en,exports.initVueI18n=function(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e=void 0!==en&&en.getLocale?en.getLocale():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new ue({locale:e,fallbackLocale:n,messages:t,watcher:o});let s=(e,t)=>{if("function"!=typeof getApp)s=function(e,t){return r.t(e,t)};else{let e=!1;s=function(t,n){const o=getApp().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return s(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>s(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}},exports.n=e=>H(e),exports.nextTick$1=Bo,exports.o=(e,t)=>ti(e,t),exports.onBackPress=Zi,exports.onLoad=Gi,exports.onMounted=_r,exports.onPullDownRefresh=Xi,exports.onReachBottom=Qi,exports.onShareAppMessage=Yi,exports.onShow=qi,exports.onUnload=Ji,exports.p=e=>function(e){const{uid:t,__counter:n}=rs();return t+","+((Zs[t]||(Zs[t]=[])).push(Yr(e))-1)+","+n}(e),exports.r=(e,t,n)=>oi(e,t,n),exports.reactive=ao,exports.ref=$o,exports.resolveComponent=function(e,t){return function(e,t,n=!0,o=!1){const r=Xo||os;if(r){const n=r.type;if("components"===e){const e=function(e,t=!0){return h(e)?e.displayName||e.name:e.name||t&&e.__name}(n,!1);if(e&&(e===t||e===O(t)||e===E(O(t))))return n}const s=er(r[e]||n[e],t)||er(r.appContext[e],t);return!s&&o?n:s}}("components",e,!0,t)||e},exports.s=e=>ei(e),exports.sr=(e,t,n)=>function(e,t,n={}){const{$templateRefs:o}=rs();o.push({i:t,r:e,k:n.k,f:n.f})}(e,t,n),exports.t=e=>(e=>g(e)?e:null==e?"":f(e)||v(e)&&(e.toString===y||!h(e.toString))?JSON.stringify(e,N,2):String(e))(e),exports.unref=So,exports.watch=nr,exports.wx$1=Yt;
