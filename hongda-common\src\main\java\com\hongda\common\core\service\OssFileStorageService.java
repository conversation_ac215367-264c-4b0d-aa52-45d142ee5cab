package com.hongda.common.core.service;

import com.aliyun.oss.OSS;
import com.hongda.common.config.AliOssConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;

@Slf4j
@Service
public class OssFileStorageService {

    @Autowired
    private OSS ossClient;

    @Autowired
    private AliOssConfigProperties aliOssConfigProperties;

    /**
     * 上传文件 (此方法无需修改)
     * @param objectName  文件名
     * @param inputStream 文件输入流
     * @return 文件的公开访问URL
     */
    public String upload(String objectName, InputStream inputStream) {
        String bucketName = aliOssConfigProperties.getBucketName();
        String endpoint = aliOssConfigProperties.getEndpoint();

        log.info("OSS文件上传开始: {}, bucket: {}", objectName, bucketName);

        try {
            // 上传文件。
            ossClient.putObject(bucketName, objectName, inputStream);
        } catch (Exception e) {
            log.error("OSS文件上传失败", e);
            throw new RuntimeException("文件上传到OSS失败", e);
        }

        // 构建文件的公开访问URL
        String url = "https://" + bucketName + "." + endpoint + "/" + objectName;
        log.info("OSS文件上传成功, URL: {}", url);
        return url;
    }

    /**
     * [🔥 核心修改] 为文件生成永久公开访问URL
     * (原 getSignedUrl 方法)
     *
     * @param input 文件的对象键（文件名）或已经是完整的URL
     * @return 永久公开的URL字符串
     */
    public String getPublicUrl(String input) {
        if (input == null || input.trim().isEmpty()) {
            return null;
        }

        // 如果输入已经是完整的URL，直接返回，不做处理
        if (input.startsWith("http://") || input.startsWith("https://")) {
            log.debug("输入已经是完整URL，直接返回: {}", input);
            return input;
        }

        // 如果输入是对象名 (objectName)，则拼接成永久公开URL
        String bucketName = aliOssConfigProperties.getBucketName();
        String endpoint = aliOssConfigProperties.getEndpoint();
        String publicUrl = "https://" + bucketName + "." + endpoint + "/" + input;

        log.debug("为对象 {} 生成公开URL: {}", input, publicUrl);
        return publicUrl;
    }
}