package com.hongda.framework.aspectj;

import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.page.TableDataInfo;
import com.hongda.common.core.service.OssFileStorageService;
import com.hongda.common.utils.StringUtils;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Collection;

@Aspect
@Component
public class OssUrlAspect {

    @Autowired
    private OssFileStorageService ossFileStorageService;

    // 定义切点，拦截所有Controller层方法的返回值
    @Pointcut("execution(public * com.hongda..*Controller.*(..))")
    public void controllerResultPointcut() {
    }

    // 在方法成功返回后执行
    @AfterReturning(returning = "result", pointcut = "controllerResultPointcut()")
    public void afterReturning(Object result) {
        // 使用try-catch避免因反射或其他意外错误影响主流程
        try {
            if (result instanceof AjaxResult) {
                // 处理 AjaxResult，兼容新旧版本可能存在的 data 或 key/value 形式
                Object data = ((AjaxResult) result).get("data");
                if (data == null) {
                    // 如果data为null, 尝试从map中直接获取 (例如 AjaxResult.success(vo) 会将vo放入map)
                    processObject(result);
                } else {
                    processObject(data);
                }
            } else if (result instanceof TableDataInfo) {
                // 处理分页数据
                Object data = ((TableDataInfo) result).getRows();
                processObject(data);
            }
        } catch (Exception e) {
            // 可以在此添加日志记录，但不向上抛出，避免影响正常业务返回
            // log.error("Error processing @OssUrl aspect", e);
        }
    }

    /**
     * 递归处理对象，将带有 @OssUrl 注解的字段转换为公开URL
     * @param obj 待处理的对象 (可以是单个对象、集合等)
     */
    private void processObject(Object obj) throws IllegalAccessException {
        if (obj == null) {
            return;
        }

        // 如果是集合类型，遍历集合中的每个元素
        if (obj instanceof Collection) {
            for (Object item : (Collection<?>) obj) {
                processObject(item); // 递归处理集合中的每一个对象
            }
            return; // 处理完集合后直接返回
        }

        // 如果是Map类型（兼容AjaxResult直接返回Map的情况）
        if (obj instanceof AjaxResult) {
            AjaxResult map = (AjaxResult) obj;
            for (Object value : map.values()) {
                processObject(value); // 递归处理Map中的每一个值
            }
            return;
        }

        // 获取当前对象的所有字段(包括父类)
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            // 允许访问私有字段
            field.setAccessible(true);

            Object fieldValue = field.get(obj);
            if (fieldValue instanceof Collection) {
                processObject(fieldValue);
            }

            // 检查字段上是否有 @OssUrl 注解
            if (field.isAnnotationPresent(OssUrl.class)) {
                if (fieldValue instanceof String && StringUtils.isNotEmpty((String) fieldValue)) {
                    // [🔥 核心修改] 调用新的方法，不再需要过期时间参数
                    String publicUrl = ossFileStorageService.getPublicUrl((String) fieldValue);
                    // 将字段的值更新为生成的公开URL
                    field.set(obj, publicUrl);
                }
            }
        }
    }
}