package com.hongda.platform.mapper;

import com.hongda.platform.domain.HongdaAd;
import java.util.List;

/**
 * 广告管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface HongdaAdMapper
{
    /**
     * 查询广告管理
     *
     * @param id 广告管理主键
     * @return 广告管理
     */
    public HongdaAd selectHongdaAdById(Long id);

    /**
     * 查询广告管理列表
     *
     * @param hongdaAd 广告管理
     * @return 广告管理集合
     */
    public List<HongdaAd> selectHongdaAdList(HongdaAd hongdaAd);

    /**
     * 新增广告管理
     *
     * @param hongdaAd 广告管理
     * @return 结果
     */
    public int insertHongdaAd(HongdaAd hongdaAd);

    /**
     * 修改广告管理
     *
     * @param hongdaAd 广告管理
     * @return 结果
     */
    public int updateHongdaAd(HongdaAd hongdaAd);

    /**
     * 删除广告管理
     *
     * @param id 广告管理主键
     * @return 结果
     */
    public int deleteHongdaAdById(Long id);

    /**
     * 批量删除广告管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaAdByIds(Long[] ids);
}