<view wx:if="{{a}}" class="highlight-container data-v-5995617f"><view class="section-header data-v-5995617f"><text class="title-main data-v-5995617f">出海</text><text class="title-gradient data-v-5995617f">国别</text></view><scroll-view class="country-selector-scroll data-v-5995617f" scroll-x show-scrollbar="{{false}}"><view class="country-selector-inner data-v-5995617f"><view wx:for="{{b}}" wx:for-item="country" wx:key="e" class="{{['country-card', 'data-v-5995617f', country.f && 'active']}}" bindtap="{{country.g}}"><image class="country-card-bg data-v-5995617f" src="{{country.a}}" mode="aspectFill"></image><view class="{{['corner-badge', 'data-v-5995617f', country.c && 'is-gold']}}" style="{{country.d}}"><text class="badge-text data-v-5995617f">{{country.b}}</text></view></view></view></scroll-view><view class="tabs-wrapper data-v-5995617f"><scroll-view class="tabs data-v-5995617f" scroll-x="true" show-scrollbar="{{false}}"><view wx:for="{{c}}" wx:for-item="tab" wx:key="c" class="{{['tab-item', 'data-v-5995617f', tab.d && 'active']}}" bindtap="{{tab.e}}" style="{{tab.f}}"><image class="tab-icon data-v-5995617f" src="{{tab.a}}"></image><text class="tab-text data-v-5995617f">{{tab.b}}</text></view></scroll-view></view><view wx:if="{{d}}" class="content-loading data-v-5995617f"><uni-load-more wx:if="{{e}}" class="data-v-5995617f" u-i="5995617f-0" bind:__l="__l" u-p="{{e}}"/></view><view wx:elif="{{f}}" class="content-display-area data-v-5995617f"><view class="content-header data-v-5995617f"><text class="content-title data-v-5995617f">{{g}}</text><view class="more-link data-v-5995617f" bindtap="{{i}}"><text class="data-v-5995617f">更多</text><uni-icons wx:if="{{h}}" class="data-v-5995617f" u-i="5995617f-1" bind:__l="__l" u-p="{{h}}"></uni-icons></view></view><view class="summary-content data-v-5995617f"><rich-text class="data-v-5995617f" nodes="{{j}}"></rich-text></view></view></view>